
import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center px-4">
      <div className="text-center">
        <div className="mb-8">
          <h1 className="text-9xl font-bold text-gray-200">404</h1>
        </div>
        
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Oops! Page Not Found</h2>
          <p className="text-lg text-gray-600 max-w-md mx-auto">
            The page you're looking for might have been moved, deleted, or is temporarily unavailable.
          </p>
        </div>
        
        <div className="space-y-4">
          <Link 
            href="/" 
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors whitespace-nowrap cursor-pointer"
          >
            <i className="ri-home-line w-5 h-5 flex items-center justify-center mr-2"></i>
            Back to Home
          </Link>
          
          <div className="flex items-center justify-center space-x-4 text-sm text-gray-500">
            <span>or try:</span>
            <Link href="/" className="text-blue-600 hover:text-blue-800 cursor-pointer whitespace-nowrap">
              Purchase Management
            </Link>
          </div>
        </div>
        
        <div className="mt-12 text-center">
          <div className="inline-flex items-center space-x-2 text-gray-400">
            <i className="ri-information-line w-4 h-4 flex items-center justify-center"></i>
            <span className="text-sm">If you believe this is an error, please check the URL</span>
          </div>
        </div>
      </div>
    </div>
  );
}
