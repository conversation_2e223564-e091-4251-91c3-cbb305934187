
'use client';

import Header from '@/components/Header';
import StatusCards from '@/components/StatusCards';
import SalesTable from '@/components/SalesTable';
import { useState, useEffect } from 'react';
import { useTranslation } from '@/hooks/useTranslation';

export default function SalesPage() {
  const { t } = useTranslation();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [navMode, setNavMode] = useState<'sidebar' | 'topnav'>('sidebar');

  // Load saved navigation mode from localStorage on component mount
  useEffect(() => {
    const savedNavMode = localStorage.getItem('navMode') as 'sidebar' | 'topnav' | null;
    if (savedNavMode) {
      setNavMode(savedNavMode);
    }
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
      <main className={`transition-all duration-300 p-6 ${
        navMode === 'topnav' ? '' : (sidebarOpen ? 'pl-64' : 'pl-16')
      }`}>
        <div className="max-w-7xl mx-auto">
          {/* Page Header */}
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">{t('sales.title')}</h1>
            <p className="text-gray-600">{t('sales.subtitle')}</p>
          </div>
          
          <StatusCards />
          <SalesTable />
        </div>
      </main>
    </div>
  );
}
