
'use client';

import { useState, useRef } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import CustomerDropdown from '@/components/CustomerDropdown';
import SalesStore, { CustomColumn } from '@/lib/salesStore';
import { useTranslation } from '@/hooks/useTranslation';

export default function NewSalesPage() {
  const { t } = useTranslation();
  const router = useRouter();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [formData, setFormData] = useState({
    customer: '',
    salesRep: '',
    deliveryDate: '',
    status: 'Pending'
  });

  const [productRows, setProductRows] = useState([
    {
      id: 'row-1',
      name: '',
      unitPrice: '',
      quantity: '1',
      quantityUnit: 'pcs',
      photo: null as File | null,
      photoPreview: '',
      customData: {} as Record<string, string>
    }
  ]);

  const [customColumns, setCustomColumns] = useState<CustomColumn[]>([]);
  const [showColumnModal, setShowColumnModal] = useState(false);
  const [newColumn, setNewColumn] = useState({
    name: '',
    type: 'text' as const,
    required: false,
    options: ['']
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Add refs for paste areas
  const pasteAreaRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // -------------------------------------------------------------------------
  // Enhanced Direct paste handling (single field or multi‑line → create rows)
  // -------------------------------------------------------------------------
  const handleDirectPaste = (
    rowId: string,
    field: string,
    e: React.ClipboardEvent<HTMLInputElement>
  ) => {
    e.preventDefault();
    e.stopPropagation();
    
    const pastedText = e.clipboardData.getData('text');
    console.log('Pasting text:', pastedText, 'into field:', field); // Debug log

    if (!pastedText || !pastedText.trim()) {
      console.log('No text to paste');
      return;
    }

    // Clean the pasted text and split by line breaks
    const lines = pastedText
      .trim()
      .split(/[\r\n]+/)
      .map(line => line.trim())
      .filter(line => line.length > 0);

    console.log('Processed lines:', lines, 'for field:', field); // Debug log

    if (lines.length === 0) {
      console.log('No valid lines found');
      return;
    }

    // Find the current row index
    const currentRowIndex = productRows.findIndex(row => row.id === rowId);
    
    if (lines.length === 1) {
      // Single value → paste into the current field only
      const cleanValue = lines[0];
      console.log('Single value paste:', cleanValue, 'into field:', field);

      if (field === 'unitPrice') {
        // Clean price value - remove currency symbols and keep only numbers and decimal
        const priceValue = cleanValue.replace(/[^\d.]/g, '');
        handleProductChange(rowId, field, priceValue);
      } else if (field === 'quantity') {
        // Clean quantity value - keep only numbers
        const qtyValue = cleanValue.replace(/[^\d]/g, '');
        handleProductChange(rowId, field, qtyValue || '1');
      } else {
        // For name or other text fields, use as-is
        handleProductChange(rowId, field, cleanValue);
      }
    } else {
      // Multiple lines → create new rows, but keep each value in the SAME COLUMN
      console.log('Multi-line paste, creating new rows for field:', field);

      // Update the current row with the first line (in the correct field)
      const firstValue = lines[0];
      if (field === 'unitPrice') {
        const priceValue = firstValue.replace(/[^\d.]/g, '');
        handleProductChange(rowId, field, priceValue);
      } else if (field === 'quantity') {
        const qtyValue = firstValue.replace(/[^\d]/g, '');
        handleProductChange(rowId, field, qtyValue || '1');
      } else {
        handleProductChange(rowId, field, firstValue);
      }

      // Build new rows for the remaining lines - SAME COLUMN ONLY
      const newRows = lines.slice(1).map((line, index) => {
        const newRow = {
          id: `row-${Date.now()}-${index}`,
          name: '',
          unitPrice: '',
          quantity: '1',
          quantityUnit: 'pcs',
          photo: null as File | null,
          photoPreview: '',
          customData: {} as Record<string, string>
        };

        // Initialize any custom column data
        customColumns.forEach(col => {
          newRow.customData[col.id] = '';
        });

        // Only fill the SAME field that was pasted into
        const cleanValue = line;
        if (field === 'name') {
          newRow.name = cleanValue;
        } else if (field === 'unitPrice') {
          newRow.unitPrice = cleanValue.replace(/[^\d.]/g, '');
        } else if (field === 'quantity') {
          newRow.quantity = cleanValue.replace(/[^\d]/g, '') || '1';
        } else if (field.startsWith('custom-')) {
          // Handle custom columns
          newRow.customData[field] = cleanValue;
        }

        return newRow;
      });

      // Insert the newly created rows after the current row
      setProductRows(prev => {
        const copy = [...prev];
        copy.splice(currentRowIndex + 1, 0, ...newRows);
        return copy;
      });

      // Inform the user
      setTimeout(() => {
        alert(
          `✅ Pasted ${lines.length} values into ${field} column and created ${newRows.length} new product rows!`
        );
      }, 100);
    }
  };

  // -------------------------------------------------------------------------
  // Enhanced Row‑paste handling (tab‑separated Excel row) - ONLY for product name field
  // -------------------------------------------------------------------------
  const handleRowPaste = (rowId: string, e: React.ClipboardEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    
    const pastedText = e.clipboardData.getData('text');
    console.log('Row paste:', pastedText); // Debug log

    if (!pastedText || !pastedText.trim()) return;

    const lines = pastedText
      .trim()
      .split(/[\r\n]+/)
      .map(line => line.trim())
      .filter(line => line.length > 0);

    console.log('Row paste lines:', lines); // Debug log

    const processLine = (line: string, targetRowId: string) => {
      if (line.includes('\t')) {
        // Tab-separated data (Excel format)
        const cells = line.split('\t').map(cell => cell.trim());
        console.log('Processing tab-separated cells:', cells);

        if (cells[0]) {
          handleProductChange(targetRowId, 'name', cells[0]);
        }
        if (cells[1]) {
          const price = cells[1].replace(/[^\d.]/g, '');
          if (price) handleProductChange(targetRowId, 'unitPrice', price);
        }
        if (cells[2]) {
          const qty = cells[2].replace(/[^\d]/g, '');
          if (qty) handleProductChange(targetRowId, 'quantity', qty);
        }

        // Custom columns (if any)
        if (cells.length > 3 && customColumns.length > 0) {
          customColumns.forEach((col, idx) => {
            const cellIdx = 3 + idx;
            if (cells[cellIdx]) {
              handleCustomDataChange(
                targetRowId,
                col.id,
                cells[cellIdx]
              );
            }
          });
        }
      } else {
        // No tab – treat whole line as product name
        console.log('Processing single line as name:', line);
        handleProductChange(targetRowId, 'name', line);
      }
    };

    // First line → current row
    if (lines.length > 0) {
      processLine(lines[0], rowId);
    }

    // Remaining lines → new rows
    if (lines.length > 1) {
      const currentRowIndex = productRows.findIndex(row => row.id === rowId);

      const newRows = lines.slice(1).map((line, idx) => ({
        id: `row-${Date.now()}-${idx}`,
        name: '',
        unitPrice: '',
        quantity: '1',
        quantityUnit: 'pcs',
        photo: null as File | null,
        photoPreview: '',
        customData: customColumns.reduce((acc, col) => {
          acc[col.id] = '';
          return acc;
        }, {} as Record<string, string>)
      }));

      // Add new rows to state
      setProductRows(prev => {
        const copy = [...prev];
        copy.splice(currentRowIndex + 1, 0, ...newRows);
        return copy;
      });

      // Process remaining lines after state update
      setTimeout(() => {
        newRows.forEach((row, idx) => {
          processLine(lines[idx + 1], row.id);
        });
      }, 10);

      setTimeout(() => {
        alert(
          `✅ Pasted ${lines.length} product rows successfully!`
        );
      }, 100);
    }
  };

  // -------------------------------------------------------------------------
  // Excel paste modal state & helpers
  // -------------------------------------------------------------------------
  const [showPasteModal, setShowPasteModal] = useState(false);
  const [pasteData, setPasteData] = useState('');
  const [pastePreview, setPastePreview] = useState<string[][]>([]);
  const [columnMapping, setColumnMapping] = useState<Record<number, string>>({});

  const handlePasteFromExcel = () => {
    setShowPasteModal(true);
    setPasteData('');
    setPastePreview([]);
    setColumnMapping({});
  };

  const processPasteData = (data: string) => {
    if (!data.trim()) {
      setPastePreview([]);
      return;
    }

    // Split into lines (ignore completely empty ones)
    const lines = data
      .trim()
      .split(/[\r\n]+/)
      .map(line => line.trim())
      .filter(line => line.length > 0);

    if (lines.length === 0) {
      setPastePreview([]);
      return;
    }

    // -------------------------------------------------
    // Detect format & build rows
    // -------------------------------------------------
    let rows: string[][] = [];

    const hasTabSeparator = lines.some(line => line.includes('\t'));
    if (hasTabSeparator) {
      // Tab‑separated (most common when copying from Excel)
      rows = lines.map(line => line.split('\t').map(cell => cell.trim()));
    } else {
      const hasCommas = lines.some(
        line => line.includes(',') && line.split(',').length > 1
      );

      if (hasCommas) {
        // CSV‑style (comma separated)
        rows = lines.map(line =>
          line.split(',').map(cell => cell.trim())
        );
      } else {
        // Single column – treat each line as a value (e.g. product name)
        rows = lines.map(line => [line]);
      }
    }

    // Remove rows that are completely empty after trimming
    const filteredRows = rows.filter(row =>
      row.some(cell => cell.length > 0)
    );

    setPastePreview(filteredRows);

    // -------------------------------------------------
    // Auto‑map columns (best‑effort based on header content)
    // -------------------------------------------------
    if (filteredRows.length > 0) {
      const autoMap: Record<number, string> = {};

      // When there is only a single column we assume it's the product name
      if (filteredRows[0].length === 1) {
        autoMap[0] = 'name';
      } else {
        // Try to infer from the first row (header)
        const header = filteredRows[0];
        header.forEach((cell, idx) => {
          const lc = cell.toLowerCase();
          if (lc.includes('product') || lc.includes('name') || lc.includes('item')) {
            autoMap[idx] = 'name';
          } else if (lc.includes('price') || lc.includes('cost') || lc.includes('amount')) {
            autoMap[idx] = 'unitPrice';
          } else if (lc.includes('quantity') || lc.includes('qty')) {
            autoMap[idx] = 'quantity';
          } else {
            // Check if any custom column matches the header text
            const custom = customColumns.find(col =>
              col.name.toLowerCase() === lc
            );
            if (custom) autoMap[idx] = custom.id;
          }
        });

        // If we still have no mapping, fall back to positional defaults
        if (Object.keys(autoMap).length === 0) {
          autoMap[0] = 'name';
          if (filteredRows[0].length > 1) autoMap[1] = 'unitPrice';
          if (filteredRows[0].length > 2) autoMap[2] = 'quantity';
        }
      }

      setColumnMapping(autoMap);
    }
  };

  const applyPasteData = () => {
    if (pastePreview.length === 0) return;

    const isSingleColumn = pastePreview[0].length === 1;
    const hasHeaderRow = !isSingleColumn && Object.keys(columnMapping).length > 0;

    // Determine where the actual data starts (skip header if we have it)
    const dataRows = isSingleColumn
      ? pastePreview
      : hasHeaderRow
        ? pastePreview.slice(1)
        : pastePreview;

    if (dataRows.length === 0) return;

    const newRows = dataRows.map((row, idx) => {
      const newRow = {
        id: `row-${Date.now()}-${idx}`,
        name: '',
        unitPrice: '',
        quantity: '1',
        quantityUnit: 'pcs',
        photo: null as File | null,
        photoPreview: '',
        customData: {} as Record<string, string>
      };

      // Initialize custom fields
      customColumns.forEach(col => {
        newRow.customData[col.id] = '';
      });

      if (isSingleColumn) {
        newRow.name = row[0] || '';
      } else {
        Object.entries(columnMapping).forEach(([colIdxStr, fieldName]) => {
          const colIdx = parseInt(colIdxStr, 10);
          const cellValue = row[colIdx] || '';

          if (fieldName === 'name') {
            newRow.name = cellValue;
          } else if (fieldName === 'unitPrice') {
            newRow.unitPrice = cellValue.replace(/[^0-9.]/g, '');
          } else if (fieldName === 'quantity') {
            newRow.quantity = cellValue.replace(/[^0-9]/g, '') || '1';
          } else {
            // Custom column – fieldName is the column id
            newRow.customData[fieldName] = cellValue;
          }
        });
      }

      return newRow;
    });

    // Discard rows without a product name
    const validRows = newRows.filter(row => row.name.trim() !== '');

    if (validRows.length === 0) {
      alert('❌ No valid product data found. Please ensure at least one column contains product names.');
      return;
    }

    // Ask the user whether to replace existing rows or append
    const replace = confirm(
      `Found ${validRows.length} products to import.\n\n✅ OK → Replace existing products\n❌ Cancel → Append to existing products`
    );

    if (replace) {
      setProductRows(validRows);
    } else {
      setProductRows(prev => [...prev, ...validRows]);
    }

    // Clean up modal state
    setShowPasteModal(false);
    setPasteData('');
    setPastePreview([]);
    setColumnMapping({});

    alert(`✅ Successfully imported ${validRows.length} products!`);
  };

  const getAvailableColumns = () => {
    const cols = [
      { id: 'name', name: 'Product Name' },
      { id: 'unitPrice', name: 'Unit Price' },
      { id: 'quantity', name: 'Quantity' },
      ...customColumns.map(col => ({ id: col.id, name: col.name }))
    ];
    return cols;
  };

  // -------------------------------------------------------------------------
  // Input handlers (form, products, custom fields, photos, etc.)
  // -------------------------------------------------------------------------
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleProductChange = (rowId: string, field: string, value: string) => {
    setProductRows(prev =>
      prev.map(row =>
        row.id === rowId ? { ...row, [field]: value } : row
      )
    );
  };

  const handleCustomDataChange = (
    rowId: string,
    columnId: string,
    value: string
  ) => {
    setProductRows(prev =>
      prev.map(row =>
        row.id === rowId
          ? { ...row, customData: { ...row.customData, [columnId]: value } }
          : row
      )
    );
  };

  const handlePhotoChange = (
    rowId: string,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = ev => {
      setProductRows(prev =>
        prev.map(row =>
          row.id === rowId
            ? { ...row, photo: file, photoPreview: ev.target?.result as string }
            : row
        )
      );
    };
    reader.readAsDataURL(file);
  };

  // Photo paste (Ctrl+V) handling
  const handlePhotoPaste = (rowId: string, e: React.ClipboardEvent) => {
    e.preventDefault();
    const items = e.clipboardData?.items;
    if (!items) return;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.type.indexOf('image') !== -1) {
        const blob = item.getAsFile();
        if (!blob) continue;

        const reader = new FileReader();
        reader.onload = ev => {
          setProductRows(prev =>
            prev.map(row =>
              row.id === rowId
                ? { ...row, photo: blob, photoPreview: ev.target?.result as string }
                : row
            )
          );
        };
        reader.readAsDataURL(blob);
        break; // only first image
      }
    }
  };

  // Drag‑and‑drop support for photos
  const handlePhotoDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handlePhotoDrop = (
    rowId: string,
    e: React.DragEvent
  ) => {
    e.preventDefault();
    e.stopPropagation();

    const files = e.dataTransfer.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    if (!file.type.startsWith('image/')) return;

    const reader = new FileReader();
    reader.onload = ev => {
      setProductRows(prev =>
        prev.map(row =>
          row.id === rowId
            ? { ...row, photo: file, photoPreview: ev.target?.result as string }
            : row
        )
      );
    };
    reader.readAsDataURL(file);
  };

  const clearPhoto = (rowId: string) => {
    setProductRows(prev =>
      prev.map(row =>
        row.id === rowId
          ? { ...row, photo: null, photoPreview: '' }
          : row
      )
    );
  };

  const addNewProductRow = () => {
    const newRow = {
      id: `row-${Date.now()}`,
      name: '',
      unitPrice: '',
      quantity: '1',
      quantityUnit: 'pcs',
      photo: null as File | null,
      photoPreview: '',
      customData: {} as Record<string, string>
    };

    // Add empty values for any defined custom columns
    customColumns.forEach(col => {
      newRow.customData[col.id] = '';
    });

    setProductRows(prev => [...prev, newRow]);
  };

  const removeProductRow = (rowId: string) => {
    if (productRows.length > 1) {
      setProductRows(prev => prev.filter(row => row.id !== rowId));
    }
  };

  const addCustomColumn = () => {
    if (!newColumn.name.trim()) return;

    const column: CustomColumn = {
      id: `custom-${Date.now()}`,
      name: newColumn.name,
      type: newColumn.type,
      required: newColumn.required,
      options:
        newColumn.type === 'select'
          ? newColumn.options.filter(opt => opt.trim())
          : undefined
    };

    setCustomColumns(prev => [...prev, column]);

    // Initialize this column for all existing rows
    setProductRows(prev =>
      prev.map(row => ({
        ...row,
        customData: { ...row.customData, [column.id]: '' }
      }))
    );

    setShowColumnModal(false);
    setNewColumn({ name: '', type: 'text', required: false, options: [''] });
  };

  const removeCustomColumn = (columnId: string) => {
    setCustomColumns(prev => prev.filter(col => col.id !== columnId));
    setProductRows(prev =>
      prev.map(row => {
        const { [columnId]: _removed, ...rest } = row.customData;
        return { ...row, customData: rest };
      })
    );
  };

  const calculateTotal = () => {
    return productRows.reduce((sum, row) => {
      const price = parseFloat(row.unitPrice) || 0;
      const qty = parseInt(row.quantity) || 0;
      return sum + price * qty;
    }, 0);
  };

  const validateForm = () => {
    setSubmitError(null);

    if (!formData.customer.trim()) {
      setSubmitError('Customer is required');
      return false;
    }
    if (!formData.salesRep.trim()) {
      setSubmitError('Sales Representative is required');
      return false;
    }

    const validProducts = productRows.filter(row => {
      const hasName = row.name.trim() !== '';
      const hasPrice = row.unitPrice !== '' && parseFloat(row.unitPrice) > 0;
      const hasQty = row.quantity !== '' && parseInt(row.quantity) > 0;
      return hasName && hasPrice && hasQty;
    });

    if (validProducts.length === 0) {
      setSubmitError(
        'At least one valid product is required (with name, price, and quantity)'
      );
      return false;
    }

    for (const product of validProducts) {
      for (const column of customColumns) {
        if (column.required) {
          const value = product.customData[column.id];
          if (!value || value.trim() === '') {
            setSubmitError(`${column.name} is required for all products`);
            return false;
          }
        }
      }
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isSubmitting) return;
    setIsSubmitting(true);
    setSubmitError(null);

    try {
      if (!validateForm()) {
        setIsSubmitting(false);
        return;
      }

      const validProducts = productRows.filter(row => {
        const hasName = row.name.trim() !== '';
        const hasPrice = row.unitPrice !== '' && parseFloat(row.unitPrice) > 0;
        const hasQty = row.quantity !== '' && parseInt(row.quantity) > 0;
        return hasName && hasPrice && hasQty;
      });

      const salesStore = SalesStore.getInstance();

      const saleData = {
        customer: formData.customer.trim(),
        salesRep: formData.salesRep.trim(),
        deliveryDate: formData.deliveryDate,
        status: formData.status,
        products: validProducts.map(p => ({
          name: p.name.trim(),
          unitPrice: p.unitPrice,
          quantity: p.quantity,
          photo: p.photoPreview || '',
          customData: p.customData || {}
        })),
        customColumns: customColumns
      };

      const newSale = salesStore.addSale(saleData);

      setShowSuccess(true);
      setTimeout(() => {
        router.push(`/sales/${newSale.id}`);
      }, 1500);
    } catch (error) {
      console.error('Error creating sales record:', error);
      setSubmitError('Failed to create sales record. Please try again.');
      setIsSubmitting(false);
    }
  };

  const isFormValid = () => {
    const hasRequiredFields =
      formData.customer.trim() && formData.salesRep.trim();

    const hasValidProducts = productRows.some(
      row =>
        row.name.trim() &&
        row.unitPrice &&
        parseFloat(row.unitPrice) > 0 &&
        row.quantity &&
        parseInt(row.quantity) > 0
    );

    return !!hasRequiredFields && hasValidProducts;
  };

  // -------------------------------------------------------------------------
  // Render
  // -------------------------------------------------------------------------
  return (
    <div className="min-h-screen bg-gray-50">
      <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

      <main
        className={`transition-all duration-300 p-6 ${
          sidebarOpen ? 'pl-64' : 'pl-16'
        }`}
      >
        <div className="max-w-6xl mx-auto">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              {t('sales.newSale.title')}
            </h2>
            <p className="text-gray-600">{t('sales.newSale.subtitle')}</p>
          </div>

          {showSuccess && (
            <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <i className="ri-check-line w-5 h-5 flex items-center justify-center text-green-600 mr-3"></i>
                <span className="text-green-800 font-medium">
                  {t('sales.newSale.successMessage')}
                </span>
              </div>
            </div>
          )}

          {submitError && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <i className="ri-error-warning-line w-5 h-5 flex items-center justify-center text-red-600 mr-3"></i>
                <span className="text-red-800 font-medium">{submitError}</span>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Sales Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('sales.newSale.salesInformation')}
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('sales.customer')} *
                  </label>
                  <CustomerDropdown
                    value={formData.customer}
                    onChange={value =>
                      setFormData(prev => ({ ...prev, customer: value }))
                    }
                    placeholder={t('sales.newSale.selectCustomer')}
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('sales.salesRep')} *
                  </label>
                  <input
                    type="text"
                    name="salesRep"
                    required
                    placeholder={t('sales.newSale.enterSalesRep')}
                    value={formData.salesRep}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('sales.deliveryDate')}
                  </label>
                  <input
                    type="date"
                    name="deliveryDate"
                    value={formData.deliveryDate}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('sales.status')}
                  </label>
                  <select
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm pr-8"
                  >
                    <option value="Pending">{t('sales.statuses.pending')}</option>
                    <option value="Confirmed">{t('sales.statuses.confirmed')}</option>
                    <option value="Shipped">{t('sales.statuses.shipped')}</option>
                    <option value="Delivered">{t('sales.statuses.delivered')}</option>
                    <option value="Cancelled">{t('sales.statuses.cancelled')}</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Products Section */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  {t('sales.products')}
                </h3>
                <div className="flex items-center space-x-3">
                  <button
                    type="button"
                    onClick={handlePasteFromExcel}
                    className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-green-600 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 cursor-pointer whitespace-nowrap"
                  >
                    <i className="ri-file-excel-2-line w-4 h-4 flex items-center justify-center"></i>
                    <span>Paste from Excel</span>
                  </button>

                  <button
                    type="button"
                    onClick={() => setShowColumnModal(true)}
                    className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-purple-600 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 cursor-pointer whitespace-nowrap"
                  >
                    <i className="ri-add-column-line w-4 h-4 flex items-center justify-center"></i>
                    <span>{t('sales.newSale.addCustomColumn')}</span>
                  </button>

                  <button
                    type="button"
                    onClick={addNewProductRow}
                    className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap"
                  >
                    <i className="ri-add-line w-4 h-4 flex items-center justify-center"></i>
                    <span>{t('sales.newSale.addProduct')}</span>
                  </button>
                </div>
              </div>

              {/* Enhanced Direct paste helper */}
              <div className="mb-4 bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <i className="ri-clipboard-line w-5 h-5 flex items-center justify-center text-green-600 mt-0.5"></i>
                  <div className="text-sm text-green-700">
                    <p className="font-medium mb-2">✨ Enhanced Direct Paste Support:</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="font-medium">🎯 Single Field Paste:</p>
                        <ul className="list-disc list-inside ml-2 space-y-1">
                          <li>Paste single values directly into any field</li>
                          <li>Paste multiple lines to create new product rows</li>
                          <li>Automatic data cleaning (prices, quantities)</li>
                        </ul>
                      </div>
                      <div>
                        <p className="font-medium">📋 Multi-Row Paste:</p>
                        <ul className="list-disc list-inside ml-2 space-y-1">
                          <li>Copy entire Excel rows (tab-separated)</li>
                          <li>Paste anywhere in a product row</li>
                          <li>Automatically maps to correct columns</li>
                        </ul>
                      </div>
                    </div>
                    <p className="mt-2 text-xs text-green-600">Green dots (●) indicate paste‑enabled fields</p>
                  </div>
                </div>
              </div>

              {/* Products table */}
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="px-4 py-3 text-xs font-semibold text-gray-700 uppercase tracking-wide border border-gray-300 text-left">
                        #
                      </th>
                      <th className="px-4 py-3 text-xs font-semibold text-gray-700 uppercase tracking-wide border border-gray-300 text-left">
                        {t('sales.newSale.photo')}
                      </th>
                      <th className="px-4 py-3 text-xs font-semibold text-gray-700 uppercase tracking-wide border border-gray-300 text-left">
                        {t('sales.newSale.productName')} <span className="text-green-500">●</span>
                      </th>
                      <th className="px-4 py-3 text-xs font-semibold text-gray-700 uppercase tracking-wide border border-gray-300 text-left">
                        {t('sales.newSale.unitPrice')} <span className="text-blue-500">●</span>
                      </th>
                      <th className="px-4 py-3 text-xs font-semibold text-gray-700 uppercase tracking-wide border border-gray-300 text-left">
                        {t('sales.newSale.quantity')} <span className="text-purple-500">●</span>
                      </th>
                      {customColumns.map(col => (
                        <th
                          key={col.id}
                          className="px-4 py-3 text-xs font-semibold text-purple-700 uppercase tracking-wide border border-gray-300 text-left"
                        >
                          <div className="flex items-center justify-between">
                            <span>{col.name}</span>
                            <button
                              type="button"
                              onClick={() => removeCustomColumn(col.id)}
                              className="text-red-400 hover:text-red-600 cursor-pointer"
                            >
                              <i className="ri-close-line w-3 h-3 flex items-center justify-center"></i>
                            </button>
                          </div>
                        </th>
                      ))}
                      <th className="px-4 py-3 text-xs font-semibold text-gray-700 uppercase tracking-wide border border-gray-300 text-left">
                        {t('sales.total')}
                      </th>
                      <th className="px-4 py-3 text-xs font-semibold text-gray-700 uppercase tracking-wide border border-gray-300 text-left">
                        {t('sales.newSale.actions')}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {productRows.map((row, idx) => (
                      <tr key={row.id} className="bg-white border-b border-gray-200">
                        <td className="px-4 py-4 border border-gray-200 text-center">
                          <span className="text-sm font-semibold text-gray-600">
                            {idx + 1}
                          </span>
                        </td>

                        <td className="px-4 py-4 border border-gray-200">
                          <div className="flex flex-col items-center">
                            <input
                              type="file"
                              accept="image/*"
                              onChange={e => handlePhotoChange(row.id, e)}
                              className="hidden"
                              id={`photo-${row.id}`}
                            />
                            <div
                              ref={el => (pasteAreaRefs.current[row.id] = el)}
                              tabIndex={0}
                              onPaste={e => handlePhotoPaste(row.id, e)}
                              onDragOver={handlePhotoDragOver}
                              onDrop={e => handlePhotoDrop(row.id, e)}
                              onClick={() =>
                                document
                                  .getElementById(`photo-${row.id}`)
                                  ?.click()
                              }
                              className="relative w-20 h-20 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50 cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-colors group focus:outline-none focus:ring-2 focus:ring-blue-500"
                              title="Click to upload, paste with Ctrl+V, or drag and drop"
                            >
                              {row.photoPreview ? (
                                <>
                                  <img
                                    src={row.photoPreview}
                                    alt="Product"
                                    className="w-full h-full object-cover rounded-lg"
                                  />
                                  <button
                                    type="button"
                                    onClick={e => {
                                      e.stopPropagation();
                                      clearPhoto(row.id);
                                    }}
                                    className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                                    title="Remove photo"
                                  >
                                    <i className="ri-close-line w-3 h-3 flex items-center justify-center"></i>
                                  </button>
                                </>
                              ) : (
                                <div className="text-center">
                                  <i className="ri-image-line w-6 h-6 flex items-center justify-center text-gray-400 mx-auto mb-1"></i>
                                  <p className="text-xs text-gray-500">Click or paste</p>
                                </div>
                              )}
                            </div>
                          </div>
                        </td>

                        {/* Enhanced Product name row - supports row paste and direct paste */}
                        <td className="px-4 py-4 border border-gray-200">
                          <div
                            className="relative"
                            onPaste={e => handleRowPaste(row.id, e)}
                            title="Paste Excel rows here (tab-separated) or single product names"
                          >
                            <input
                              type="text"
                              required
                              placeholder={t('sales.newSale.productNamePlaceholder')}
                              value={row.name}
                              onChange={e =>
                                handleProductChange(row.id, 'name', e.target.value)
                              }
                              onPaste={e => handleDirectPaste(row.id, 'name', e)}
                              className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                            />
                            <div className="absolute -top-1 -right-1">
                              <div className="w-2 h-2 bg-green-500 rounded-full shadow-sm" title="Direct paste enabled - Product names"></div>
                            </div>
                          </div>
                        </td>

                        {/* Enhanced Unit price - supports direct paste */}
                        <td className="px-4 py-4 border border-gray-200">
                          <div className="relative">
                            <input
                              type="number"
                              required
                              min="0"
                              step="0.01"
                              placeholder="0.00"
                              value={row.unitPrice}
                              onChange={e =>
                                handleProductChange(row.id, 'unitPrice', e.target.value)
                              }
                              onPaste={e => handleDirectPaste(row.id, 'unitPrice', e)}
                              className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                            />
                            <div className="absolute -top-1 -right-1">
                              <div className="w-2 h-2 bg-blue-500 rounded-full shadow-sm" title="Direct paste enabled - Unit prices only"></div>
                            </div>
                          </div>
                        </td>

                        {/* Enhanced Quantity - supports direct paste */}
                        <td className="px-4 py-4 border border-gray-200">
                          <div className="flex items-center space-x-2">
                            <div className="flex-1 relative">
                              <input
                                type="number"
                                required
                                min="1"
                                placeholder="1"
                                value={row.quantity}
                                onChange={e =>
                                  handleProductChange(row.id, 'quantity', e.target.value)
                                }
                                onPaste={e => handleDirectPaste(row.id, 'quantity', e)}
                                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                              />
                              <div className="absolute -top-1 -right-1">
                                <div className="w-2 h-2 bg-purple-500 rounded-full shadow-sm" title="Direct paste enabled - Quantities only"></div>
                              </div>
                            </div>
                            <span className="text-xs text-gray-500">{row.quantityUnit}</span>
                          </div>
                        </td>

                        {/* Custom columns with paste support */}
                        {customColumns.map(col => (
                          <td key={col.id} className="px-4 py-4 border border-gray-200">
                            {col.type === 'select' ? (
                              <select
                                value={row.customData[col.id] || ''}
                                onChange={e =>
                                  handleCustomDataChange(row.id, col.id, e.target.value)
                                }
                                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm pr-8"
                              >
                                <option value="">{t('sales.newSale.selectOption')}</option>
                                {col.options?.map((opt, i) => (
                                  <option key={i} value={opt}>
                                    {opt}
                                  </option>
                                ))}
                              </select>
                            ) : (
                              <div className="relative">
                                <input
                                  type={col.type}
                                  placeholder={`${t('sales.newSale.enter')} ${col.name.toLowerCase()}`}
                                  value={row.customData[col.id] || ''}
                                  onChange={e =>
                                    handleCustomDataChange(row.id, col.id, e.target.value)
                                  }
                                  onPaste={e => handleDirectPaste(row.id, col.id, e)}
                                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                                />
                                <div className="absolute -top-1 -right-1">
                                  <div className="w-2 h-2 bg-orange-500 rounded-full shadow-sm" title={`Direct paste enabled - ${col.name} only`}></div>
                                </div>
                              </div>
                            )}
                          </td>
                        ))}

                        {/* Row total */}
                        <td className="px-4 py-4 border border-gray-200 text-right">
                          <span className="text-sm font-semibold text-green-600">
                            ${(
                              (parseFloat(row.unitPrice) || 0) *
                              (parseInt(row.quantity) || 0)
                            ).toFixed(2)}
                          </span>
                        </td>

                        {/* Actions */}
                        <td className="px-4 py-4 border border-gray-200 text-center">
                          {productRows.length > 1 && (
                            <button
                              type="button"
                              onClick={() => removeProductRow(row.id)}
                              className="px-3 py-1.5 text-xs font-medium text-red-600 bg-white border border-red-200 rounded hover:bg-red-50 cursor-pointer"
                              title="Delete this product row"
                            >
                              <i className="ri-delete-bin-line w-3 h-3 flex items-center justify-center"></i>
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Total summary */}
              <div className="mt-6 flex justify-end">
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-700 mb-1">
                      {t('sales.newSale.totalAmount')}
                    </div>
                    <div className="text-2xl font-bold text-green-600">
                      ${calculateTotal().toFixed(2)}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Submit / Cancel */}
            <div className="flex items-center justify-end space-x-4">
              <Link
                href="/sales"
                className="px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap"
              >
                {t('common.cancel')}
              </Link>

              <button
                type="submit"
                disabled={isSubmitting || !isFormValid()}
                className="px-6 py-3 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer whitespace-nowrap flex items-center space-x-2"
              >
                {isSubmitting ? (
                  <>
                    <i className="ri-loader-4-line w-4 h-4 flex items-center justify-center animate-spin"></i>
                    <span>{t('sales.newSale.creating')}</span>
                  </>
                ) : (
                  <>
                    <i className="ri-add-line w-4 h-4 flex items-center justify-center"></i>
                    <span>{t('sales.newSale.createSalesRecord')}</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </main>

      {/* Add Column Modal */}
      {showColumnModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-lg mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                {t('sales.newSale.addCustomColumnModal.title')}
              </h3>
              <button
                type="button"
                onClick={() => setShowColumnModal(false)}
                className="text-gray-400 hover:text-gray-600 cursor-pointer"
              >
                <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('sales.newSale.addCustomColumnModal.columnName')}
                </label>
                <input
                  type="text"
                  required
                  value={newColumn.name}
                  onChange={e =>
                    setNewColumn(prev => ({ ...prev, name: e.target.value }))
                  }
                  placeholder={t('sales.newSale.addCustomColumnModal.enterColumnName')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('sales.newSale.addCustomColumnModal.dataType')}
                </label>
                <select
                  value={newColumn.type}
                  onChange={e =>
                    setNewColumn(prev => ({
                      ...prev,
                      type: e.target.value as any
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-8"
                >
                  <option value="text">{t('sales.newSale.addCustomColumnModal.text')}</option>
                  <option value="number">{t('sales.newSale.addCustomColumnModal.number')}</option>
                  <option value="date">{t('sales.newSale.addCustomColumnModal.date')}</option>
                  <option value="select">{t('sales.newSale.addCustomColumnModal.dropdown')}</option>
                </select>
              </div>

              {newColumn.type === 'select' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('sales.newSale.addCustomColumnModal.options')}
                  </label>
                  <div className="space-y-2">
                    {newColumn.options.map((opt, i) => (
                      <div key={i} className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={opt}
                          onChange={e => {
                            const newOpts = [...newColumn.options];
                            newOpts[i] = e.target.value;
                            setNewColumn(prev => ({
                              ...prev,
                              options: newOpts
                            }));
                          }}
                          placeholder={`${t('sales.newSale.addCustomColumnModal.option')} ${i + 1}`}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                        />
                        {newColumn.options.length > 1 && (
                          <button
                            type="button"
                            onClick={() => {
                              const newOpts = newColumn.options.filter(
                                (_, idx) => idx !== i
                              );
                              setNewColumn(prev => ({
                                ...prev,
                                options: newOpts
                              }));
                            }}
                            className="p-2 text-red-500 hover:text-red-700 cursor-pointer"
                          >
                            <i className="ri-close-line w-4 h-4 flex items-center justify-center"></i>
                          </button>
                        )}
                      </div>
                    ))}
                    <button
                      type="button"
                      onClick={() =>
                        setNewColumn(prev => ({
                          ...prev,
                          options: [...prev.options, '']
                        }))
                      }
                      className="flex items-center space-x-2 px-3 py-2 text-sm text-blue-600 hover:text-blue-800 cursor-pointer"
                    >
                      <i className="ri-add-line w-3 h-3 flex items-center justify-center"></i>
                      <span>{t('sales.newSale.addCustomColumnModal.addOption')}</span>
                    </button>
                  </div>
                </div>
              )}

              <div className="flex items-center justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowColumnModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer"
                >
                  {t('common.cancel')}
                </button>
                <button
                  type="button"
                  onClick={addCustomColumn}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 cursor-pointer"
                >
                  {t('sales.newSale.addCustomColumnModal.addColumn')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Excel Paste Modal */}
      {showPasteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Paste from Excel</h3>
              <button
                onClick={() => setShowPasteModal(false)}
                className="text-gray-400 hover:text-gray-600 cursor-pointer"
              >
                <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
              </button>
            </div>

            <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
              <div className="space-y-6">
                {/* Instructions */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <i className="ri-information-line w-5 h-5 flex items-center justify-center text-blue-600 mt-0.5"></i>
                    <div>
                      <h4 className="text-sm font-semibold text-blue-900 mb-2">
                        How to paste from Excel:
                      </h4>
                      <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                        <li>Select and copy your data from Excel (Ctrl+C)</li>
                        <li>Paste the data in the text area below (Ctrl+V)</li>
                        <li>Map columns to the correct fields</li>
                        <li>Click "Apply Data" to import</li>
                      </ol>
                      <p className="text-xs text-blue-700 mt-2">
                        Note: Photos must be uploaded individually after importing the data.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Paste area */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Paste Excel Data Here
                  </label>
                  <textarea
                    value={pasteData}
                    onChange={e => {
                      setPasteData(e.target.value);
                      processPasteData(e.target.value);
                    }}
                    placeholder="Paste your Excel data here... (Ctrl+V)"
                    rows={8}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm font-mono resize-none"
                  />
                </div>

                {/* Mapping & Preview */}
                {pastePreview.length > 0 && (
                  <div>
                    <h4 className="text-sm font-semibold text-gray-900 mb-3">
                      Column Mapping
                    </h4>

                    {/* Mapping selectors */}
                    <div className="bg-gray-50 rounded-lg p-4 mb-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {pastePreview[0].map((header, idx) => (
                          <div key={idx} className="space-y-2">
                            <label className="block text-xs font-medium text-gray-700">
                              Excel Column {idx + 1}: "{header}"
                            </label>
                            <select
                              value={columnMapping[idx] || ''}
                              onChange={e =>
                                setColumnMapping(prev => ({
                                  ...prev,
                                  [idx]: e.target.value
                                }))
                              }
                              className="w-full px-3 py-2 border border-gray-300 rounded text-xs focus:outline-none focus:ring-1 focus:ring-blue-500 pr-8"
                            >
                              <option value="">Skip this column</option>
                              {getAvailableColumns().map(col => (
                                <option key={col.id} value={col.id}>
                                  {col.name}
                                </option>
                              ))}
                            </select>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Data preview */}
                    <h4 className="text-sm font-semibold text-gray-900 mb-3">
                      Data Preview ({pastePreview.length} rows)
                    </h4>
                    <div className="border border-gray-200 rounded-lg overflow-hidden">
                      <div className="overflow-x-auto max-h-64">
                        <table className="min-w-full text-xs">
                          <thead className="bg-gray-50">
                            <tr>
                              {pastePreview[0].map((header, idx) => (
                                <th
                                  key={idx}
                                  className="px-3 py-2 text-left font-medium text-gray-700 border-r border-gray-200 last:border-r-0"
                                >
                                  <div className="space-y-1">
                                    <div className="truncate">{header}</div>
                                    <div className="text-xs text-blue-600">
                                      {columnMapping[idx]
                                        ? getAvailableColumns().find(
                                            c => c.id === columnMapping[idx]
                                          )?.name || 'Unknown'
                                        : 'Skipped'}
                                    </div>
                                  </div>
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-100">
                            {pastePreview.slice(1, 6).map((row, rIdx) => (
                              <tr key={rIdx} className="hover:bg-gray-50">
                                {row.map((cell, cIdx) => (
                                  <td
                                    key={cIdx}
                                    className="px-3 py-2 text-gray-900 border-r border-gray-100 last:border-r-0"
                                  >
                                    <div className="truncate max-w-32" title={cell}>
                                      {cell}
                                    </div>
                                  </td>
                                ))}
                              </tr>
                            ))}
                            {pastePreview.length > 6 && (
                              <tr>
                                <td
                                  colSpan={pastePreview[0].length}
                                  className="px-3 py-2 text-center text-gray-500 italic"
                                >
                                  ... and {pastePreview.length - 6} more rows
                                </td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Footer actions */}
            <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
              <button
                type="button"
                onClick={() => setShowPasteModal(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={applyPasteData}
                disabled={
                  pastePreview.length === 0 || Object.keys(columnMapping).length === 0
                }
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer whitespace-nowrap"
              >
                Apply Data ({pastePreview.length -
                  (Object.keys(columnMapping).length > 0 ? 1 : 0)}{' '}
                products)
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
