
import SalesDetailClient from './SalesDetailClient';

export async function generateStaticParams() {
  return [
    { id: 'SO-2024-001' },
    { id: 'SO-2024-002' },
    { id: 'SO-2024-003' },
    { id: 'SO-2024-004' },
    { id: 'SO-2024-005' },
    { id: 'SO-2024-006' },
    { id: 'SO-2024-007' },
    { id: 'SO-2024-008' },
    { id: 'SO-2025-009' },
    { id: 'SO-2025-010' },
    { id: 'SO-2025-011' },
    { id: 'SO-2025-012' },
    { id: 'SO-2025-013' },
    { id: 'SO-2025-014' },
    { id: 'SO-2025-015' },
    { id: 'SO-2025-016' },
    { id: 'SO-2025-017' },
    { id: 'SO-2025-018' },
    { id: 'SO-2025-019' },
    { id: 'SO-2025-020' },
  ];
}

export default function SalesDetailPage({ params }: { params: { id: string } }) {
  return <SalesDetailClient saleId={params.id} />;
}
