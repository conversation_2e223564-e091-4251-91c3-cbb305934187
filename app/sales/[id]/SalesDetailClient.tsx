
'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import SalesStore, { Sale, Product, CustomColumn } from '@/lib/salesStore';

interface SalesDetailClientProps {
  saleId: string;
}

export default function SalesDetailClient({ saleId }: SalesDetailClientProps) {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [sale, setSale] = useState<Sale | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editingProduct, setEditingProduct] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // States for custom columns & export
  const [showColumnModal, setShowColumnModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [newColumn, setNewColumn] = useState({
    name: '',
    type: 'text' as const,
    required: false,
    options: ['']
  });
  const [exportFormat, setExportFormat] = useState<'excel' | 'html'>('html');
  const [selectedExportColumns, setSelectedExportColumns] = useState([
    'orderInfo',
    'customer',
    'salesRep',
    'salesDate',
    'products',
    'total',
    'status'
  ]);

  // New Product States
  const [showAddProductModal, setShowAddProductModal] = useState(false);
  const [newProduct, setNewProduct] = useState({
    name: '',
    sku: '',
    unitPrice: '',
    quantity: 1,
    photo: null as File | null,
    photoPreview: '',
    customData: {} as Record<string, string>
  });

  // WeChat Integration States
  const [showWeChatModal, setShowWeChatModal] = useState(false);
  const [wechatConfig, setWechatConfig] = useState({
    recipientOpenId: '',
    recipientName: '',
    message: '',
    sendType: 'template' as 'template' | 'direct'
  });
  const [isSendingWeChat, setIsSendingWeChat] = useState(false);
  const [wechatSendResult, setWechatSendResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  // WhatsApp Integration States
  const [showWhatsAppModal, setShowWhatsAppModal] = useState(false);
  const [whatsappConfig, setWhatsappConfig] = useState({
    phoneNumber: '',
    recipientName: '',
    message: '',
    sendType: 'template' as 'template' | 'direct'
  });
  const [isSendingWhatsApp, setIsSendingWhatsApp] = useState(false);
  const [whatsappSendResult, setWhatsappSendResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  const [editForm, setEditForm] = useState({
    customer: '',
    salesRep: '',
    deliveryDate: '',
    status: ''
  });

  const [productEditForm, setProductEditForm] = useState<Product>({
    name: '',
    sku: '',
    unitPrice: '',
    quantity: 0,
    total: '',
    photo: '',
    customData: {}
  });

  // -------------------------------------------------------------------------
  // Initialise / subscribe to the sales data
  // -------------------------------------------------------------------------
  useEffect(() => {
    const salesStore = SalesStore.getInstance();

    const loadSale = () => {
      const saleData = salesStore.getSaleById(saleId);
      if (saleData) {
        setSale(saleData);
        setEditForm({
          customer: saleData.customer,
          salesRep: saleData.salesRep || saleData.vendor,
          deliveryDate: saleData.deliveryDate || '',
          status: saleData.status
        });
      }
    };

    loadSale();
    setIsLoading(false);

    const unsubscribe = salesStore.subscribe(() => {
      loadSale();
    });

    return unsubscribe;
  }, [saleId]);

  // -------------------------------------------------------------------------
  // Add New Product functionality
  // -------------------------------------------------------------------------
  const handleAddProduct = () => {
    if (!sale || !newProduct.name.trim() || !newProduct.unitPrice) return;

    const unitPrice = parseFloat(newProduct.unitPrice) || 0;
    const quantity = newProduct.quantity || 1;
    const total = unitPrice * quantity;

    const productToAdd: Product = {
      name: newProduct.name,
      sku:
        newProduct.sku ||
        `SKU-${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
      unitPrice: `$${unitPrice.toFixed(2)}`,
      quantity: quantity,
      total: `$${total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
      photo: newProduct.photoPreview,
      customData: newProduct.customData
    };

    try {
      const salesStore = SalesStore.getInstance();

      // Add the new product to the sale
      const updatedProducts = [...sale.products, productToAdd];

      // Recalculate the sale total
      const newSaleTotal = updatedProducts.reduce((sum, prod) => {
        return sum + parseFloat(prod.total.replace(/[$,]/g, ''));
      }, 0);

      // Update the sale with new product and recalculated totals
      salesStore.updateSale(saleId, {
        products: updatedProducts,
        total: `$${newSaleTotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
        commission: `$${(newSaleTotal * 0.1).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
      });

      // Reset form and close modal
      setNewProduct({
        name: '',
        sku: '',
        unitPrice: '',
        quantity: 1,
        photo: null,
        photoPreview: '',
        customData: {}
      });
      setShowAddProductModal(false);
    } catch (error) {
      console.error('Failed to add product:', error);
    }
  };

  const handleNewProductPhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setNewProduct(prev => ({
          ...prev,
          photo: file,
          photoPreview: e.target?.result as string
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const removeNewProductPhoto = () => {
    setNewProduct(prev => ({
      ...prev,
      photo: null,
      photoPreview: ''
    }));
  };

  const handleNewProductCustomDataChange = (columnId: string, value: string) => {
    setNewProduct(prev => ({
      ...prev,
      customData: { ...prev.customData, [columnId]: value }
    }));
  };

  // -------------------------------------------------------------------------
  // Custom column handling
  // -------------------------------------------------------------------------
  const addCustomColumn = () => {
    if (!newColumn.name.trim() || !sale) return;

    const column: CustomColumn = {
      id: `custom-${Date.now()}`,
      name: newColumn.name,
      type: newColumn.type,
      required: newColumn.required,
      options:
        newColumn.type === 'select'
          ? newColumn.options.filter(opt => opt.trim())
          : undefined
    };

    try {
      const salesStore = SalesStore.getInstance();
      salesStore.addCustomColumn(saleId, column);
    } catch (error) {
      console.error('Failed to add custom column:', error);
    }

    setShowColumnModal(false);
    setNewColumn({ name: '', type: 'text', required: false, options: [''] });
  };

  const removeCustomColumn = (columnId: string) => {
    try {
      const salesStore = SalesStore.getInstance();
      salesStore.removeCustomColumn(saleId, columnId);
    } catch (error) {
      console.error('Failed to remove custom column:', error);
    }
  };

  const addSelectOption = () => {
    setNewColumn(prev => ({
      ...prev,
      options: [...prev.options, '']
    }));
  };

  const updateSelectOption = (index: number, value: string) => {
    setNewColumn(prev => ({
      ...prev,
      options: prev.options.map((opt, i) => (i === index ? value : opt))
    }));
  };

  const removeSelectOption = (index: number) => {
    if (newColumn.options.length > 1) {
      setNewColumn(prev => ({
        ...prev,
        options: prev.options.filter((_, i) => i !== index)
      }));
    }
  };

  // -------------------------------------------------------------------------
  // Enhanced Export handling - Clone from other pages
  // -------------------------------------------------------------------------
  const getAvailableExportColumns = () => {
    const baseColumns = [
      { id: 'orderInfo', name: 'Order Information' },
      { id: 'customer', name: 'Customer' },
      { id: 'salesRep', name: 'Sales Representative' },
      { id: 'salesDate', name: 'Sales Date' },
      { id: 'deliveryDate', name: 'Delivery Date' },
      { id: 'total', name: 'Total Amount' },
      { id: 'commission', name: 'Commission' },
      { id: 'profit', name: 'Profit' },
      { id: 'status', name: 'Status' },
      { id: 'products', name: 'Products List' },
      { id: 'productDetails', name: 'Detailed Products' },
      { id: 'financialSummary', name: 'Financial Summary' }
    ];

    if (sale?.customColumns) {
      const customCols = sale.customColumns.map(col => ({
        id: `custom-${col.id}`,
        name: `Custom: ${col.name}`
      }));
      return [...baseColumns, ...customCols];
    }

    return baseColumns;
  };

  const generateOrderExport = () => {
    if (!sale) return {};

    const calculateProfit = () => {
      const totalAmount = parseFloat(sale.total.replace(/[$,]/g, ''));
      const commissionAmount = parseFloat(sale.commission.replace(/[$,]/g, ''));
      return totalAmount - commissionAmount;
    };

    return {
      orderInfo: {
        id: sale.id,
        orderDate: sale.salesDate,
        generatedDate: new Date().toLocaleDateString(),
        generatedTime: new Date().toLocaleTimeString()
      },
      customer: sale.customer,
      salesRep: sale.salesRep || sale.vendor,
      salesDate: sale.salesDate,
      deliveryDate: sale.deliveryDate || 'Not specified',
      total: sale.total,
      commission: sale.commission,
      profit: `$${calculateProfit().toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
      status: sale.status,
      products: sale.products,
      customColumns: sale.customColumns || [],
      financialSummary: {
        subtotal: sale.total,
        commission: sale.commission,
        profit: `$${calculateProfit().toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
        productCount: sale.products.length
      }
    };
  };

  const downloadOrderHTML = (data: any) => {
    const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Order ${sale?.id}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .report { background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 40px; text-align: center; }
        .header h1 { margin: 0; font-size: 32px; font-weight: 300; }
        .header .subtitle { margin: 15px 0 0 0; opacity: 0.9; font-size: 16px; }
        .section { padding: 30px; border-bottom: 1px solid #e9ecef; }
        .section:last-child { border-bottom: none; }
        .section-title { color: #495057; font-size: 20px; font-weight: 600; margin-bottom: 20px; border-bottom: 2px solid #3b82f6; padding-bottom: 10px; }
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .info-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #3b82f6; }
        .info-card h4 { margin: 0 0 10px 0; color: #495057; font-size: 14px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; }
        .info-card p { margin: 0; color: #6c757d; font-size: 16px; font-weight: 500; }
        .products-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        .products-table th { background: #f8f9fa; padding: 15px 8px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 11px; text-transform: uppercase; letter-spacing: 0.5px; }
        .products-table td { padding: 15px 8px; border-bottom: 1px solid #e9ecef; color: #6c757d; font-size: 13px; }
        .products-table tr:hover { background: #f8f9fa; }
        .financial-summary { background: #f8f9fa; padding: 30px; margin-top: 20px; border-top: 3px solid #3b82f6; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
        .summary-card { background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 25px; border-radius: 8px; text-align: center; }
        .summary-card.warning { background: linear-gradient(135deg, #ffc107, #fd7e14); }
        .summary-card.info { background: linear-gradient(135deg, #17a2b8, #6f42c1); }
        .summary-card h3 { margin: 0 0 10px 0; font-size: 14px; font-weight: 400; opacity: 0.9; text-transform: uppercase; letter-spacing: 0.5px; }
        .summary-card .amount { font-size: 24px; font-weight: 600; margin: 0; }
        .status-badge { display: inline-block; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 600; text-transform: uppercase; }
        .status-completed { background: #d1fae5; color: #065f46; }
        .status-processing { background: #dbeafe; color: #1e40af; }
        .status-shipped { background: #e0e7ff; color: #5b21b6; }
        .status-delivered { background: #f3f4f6; color: #374151; }
        .custom-column { background: #e3f2fd; font-weight: 500; }
        .footer { background: #343a40; color: white; padding: 20px 30px; text-align: center; font-size: 12px; }
        @media print { body { background: white; } .report { box-shadow: none; } }
    </style>
</head>
<body>
    <div class="container">
        <div class="report">
            <div class="header">
                <h1>Sales Order Report</h1>
                <div class="subtitle">Order ID: ${data.orderInfo.id}</div>
                <div class="subtitle">Generated on ${data.orderInfo.generatedDate} at ${data.orderInfo.generatedTime}</div>
            </div>`;

    // Order Information Section
    if (selectedExportColumns.includes('orderInfo') || selectedExportColumns.includes('customer') || selectedExportColumns.includes('salesRep')) {
      htmlContent += `
            <div class="section">
                <div class="section-title">Order Information</div>
                <div class="info-grid">`;
      
      if (selectedExportColumns.includes('customer')) {
        htmlContent += `
                    <div class="info-card">
                        <h4>Customer</h4>
                        <p>${data.customer}</p>
                    </div>`;
      }
      
      if (selectedExportColumns.includes('salesRep')) {
        htmlContent += `
                    <div class="info-card">
                        <h4>Sales Representative</h4>
                        <p>${data.salesRep}</p>
                    </div>`;
      }
      
      if (selectedExportColumns.includes('salesDate')) {
        htmlContent += `
                    <div class="info-card">
                        <h4>Sales Date</h4>
                        <p>${data.salesDate}</p>
                    </div>`;
      }
      
      if (selectedExportColumns.includes('deliveryDate')) {
        htmlContent += `
                    <div class="info-card">
                        <h4>Delivery Date</h4>
                        <p>${data.deliveryDate}</p>
                    </div>`;
      }
      
      if (selectedExportColumns.includes('status')) {
        const statusClass = data.status.toLowerCase().replace(' ', '-');
        htmlContent += `
                    <div class="info-card">
                        <h4>Status</h4>
                        <p><span class="status-badge status-${statusClass}">${data.status}</span></p>
                    </div>`;
      }
      
      htmlContent += `
                </div>
            </div>`;
    }

    // Products Section
    if (selectedExportColumns.includes('products') || selectedExportColumns.includes('productDetails')) {
      htmlContent += `
            <div class="section">
                <div class="section-title">Product Details</div>
                <table class="products-table">
                    <thead>
                        <tr>
                            <th>Product Name</th>
                            <th>SKU</th>
                            <th>Unit Price</th>
                            <th>Quantity</th>`;
      
      // Add custom column headers
      if (data.customColumns && data.customColumns.length > 0) {
        data.customColumns.forEach((col: any) => {
          htmlContent += `<th class="custom-column">${col.name}</th>`;
        });
      }
      
      htmlContent += `
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>`;
      
      data.products.forEach((product: any) => {
        htmlContent += `
                        <tr>
                            <td><strong>${product.name}</strong></td>
                            <td>${product.sku || 'N/A'}</td>
                            <td>${product.unitPrice}</td>
                            <td>${product.quantity}</td>`;
        
        // Add custom column data
        if (data.customColumns && data.customColumns.length > 0) {
          data.customColumns.forEach((col: any) => {
            const customValue = product.customData?.[col.id] || 'N/A';
            let displayValue = customValue;
            
            if (col.type === 'date' && customValue && customValue !== 'N/A') {
              displayValue = new Date(customValue).toLocaleDateString();
            } else if (col.name === 'Color' && customValue !== 'N/A') {
              displayValue = `● ${customValue}`;
            }
            
            htmlContent += `<td class="custom-column">${displayValue}</td>`;
          });
        }
        
        htmlContent += `
                            <td><strong>${product.total}</strong></td>
                        </tr>`;
      });
      
      htmlContent += `
                    </tbody>
                </table>
            </div>`;
    }

    // Financial Summary Section
    if (selectedExportColumns.includes('financialSummary') || selectedExportColumns.includes('total') || selectedExportColumns.includes('commission') || selectedExportColumns.includes('profit')) {
      htmlContent += `
            <div class="section">
                <div class="section-title">Financial Summary</div>
                <div class="summary-grid">`;
      
      if (selectedExportColumns.includes('total')) {
        htmlContent += `
                    <div class="summary-card">
                        <h3>Total Amount</h3>
                        <p class="amount">${data.total}</p>
                    </div>`;
      }
      
      if (selectedExportColumns.includes('commission')) {
        htmlContent += `
                    <div class="summary-card warning">
                        <h3>Commission</h3>
                        <p class="amount">${data.commission}</p>
                    </div>`;
      }
      
      if (selectedExportColumns.includes('profit')) {
        htmlContent += `
                    <div class="summary-card info">
                        <h3>Profit</h3>
                        <p class="amount">${data.profit}</p>
                    </div>`;
      }
      
      htmlContent += `
                    <div class="summary-card">
                        <h3>Products</h3>
                        <p class="amount">${data.financialSummary.productCount}</p>
                    </div>
                </div>
            </div>`;
    }

    htmlContent += `
            <div class="footer">
                Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()} | Sales Order Management System
            </div>
        </div>
    </div>
</body>
</html>`;

    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sales-order-${sale?.id}-${new Date().toISOString().split('T')[0]}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const downloadOrderExcel = (data: any) => {
    let htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        table { 
            border-collapse: collapse; 
            width: 100%; 
            table-layout: fixed !important;
        }
        th, td { 
            border: 1px solid #000; 
            padding: 8px; 
            text-align: left; 
            vertical-align: middle !important;
            word-wrap: break-word;
            overflow: hidden !important;
        }
        th { 
            background-color: #f2f2f2; 
            font-weight: bold; 
            height: 40px !important;
        }
        .header-section { margin-bottom: 20px; }
        .section-title { font-size: 16px; font-weight: bold; margin: 20px 0 10px 0; color: #333; }
        .summary-section { margin-top: 20px; background-color: #f9f9f9; padding: 10px; }
        .product-photo { 
            width: 80px !important; 
            height: 60px !important; 
            object-fit: contain !important;
            border: 1px solid #ddd; 
            display: block !important;
            margin: 0 auto !important;
            position: relative !important;
        }
        .photo-cell {
            width: 90px !important;
            height: 80px !important;
            text-align: center !important;
            vertical-align: middle !important;
            padding: 10px !important;
            position: relative !important;
            overflow: hidden !important;
        }
        .product-row {
            height: 80px !important;
        }
        .no-photo {
            width: 80px !important;
            height: 60px !important;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
            margin: 0 auto !important;
        }
    </style>
</head>
<body>
    <div class="header-section">
        <h1>Sales Order Export - ${data.orderInfo.id}</h1>
        <p><strong>Generated:</strong> ${data.orderInfo.generatedDate} ${data.orderInfo.generatedTime}</p>
        <p><strong>Customer:</strong> ${data.customer}</p>
        <p><strong>Sales Rep:</strong> ${data.salesRep}</p>
        <p><strong>Status:</strong> ${data.status}</p>
    </div>`;

    // Order Information Table
    if (selectedExportColumns.some(col => ['customer', 'salesRep', 'salesDate', 'deliveryDate', 'status'].includes(col))) {
      htmlContent += `
    <div class="section-title">Order Information</div>
    <table>
        <colgroup>`;
      
      selectedExportColumns.forEach(columnId => {
        switch (columnId) {
          case 'customer':
          case 'salesRep':
          case 'salesDate':
          case 'deliveryDate':
          case 'status':
            htmlContent += `<col width="200">`;
            break;
        }
      });
      
      htmlContent += `
        </colgroup>
        <thead>
            <tr>`;
      
      selectedExportColumns.forEach(columnId => {
        switch (columnId) {
          case 'customer':
            htmlContent += `<th>Customer</th>`;
            break;
          case 'salesRep':
            htmlContent += `<th>Sales Representative</th>`;
            break;
          case 'salesDate':
            htmlContent += `<th>Sales Date</th>`;
            break;
          case 'deliveryDate':
            htmlContent += `<th>Delivery Date</th>`;
            break;
          case 'status':
            htmlContent += `<th>Status</th>`;
            break;
        }
      });
      
      htmlContent += `
            </tr>
        </thead>
        <tbody>
            <tr>`;
      
      selectedExportColumns.forEach(columnId => {
        switch (columnId) {
          case 'customer':
            htmlContent += `<td>${data.customer}</td>`;
            break;
          case 'salesRep':
            htmlContent += `<td>${data.salesRep}</td>`;
            break;
          case 'salesDate':
            htmlContent += `<td>${data.salesDate}</td>`;
            break;
          case 'deliveryDate':
            htmlContent += `<td>${data.deliveryDate}</td>`;
            break;
          case 'status':
            htmlContent += `<td>${data.status}</td>`;
            break;
        }
      });
      
      htmlContent += `
            </tr>
        </tbody>
    </table>`;
    }

    // Products Table with Fixed Image Dimensions
    if (selectedExportColumns.includes('products') || selectedExportColumns.includes('productDetails')) {
      htmlContent += `
    <div class="section-title">Products</div>
    <table>
        <colgroup>
            <col width="90">
            <col width="200">
            <col width="120">
            <col width="100">
            <col width="80">`;
      
      // Add custom column widths
      if (data.customColumns && data.customColumns.length > 0) {
        data.customColumns.forEach((col: any) => {
          htmlContent += `<col width="120">`;
        });
      }
      
      htmlContent += `
            <col width="100">
        </colgroup>
        <thead>
            <tr>
                <th class="photo-cell">Photo</th>
                <th>Product Name</th>
                <th>SKU</th>
                <th>Unit Price</th>
                <th>Quantity</th>`;
      
      // Add custom column headers
      if (data.customColumns && data.customColumns.length > 0) {
        data.customColumns.forEach((col: any) => {
          htmlContent += `<th>${col.name}</th>`;
        });
      }
      
      htmlContent += `
                <th>Total</th>
            </tr>
        </thead>
        <tbody>`;
      
      data.products.forEach((product: any) => {
        htmlContent += `
            <tr class="product-row">
                <td class="photo-cell">`;
        
        if (product.photo && product.photo.trim() !== '') {
          htmlContent += `<img src="${product.photo}" alt="${product.name}" class="product-photo" width="80" height="60" />`;
        } else {
          htmlContent += `<div class="no-photo">No Image</div>`;
        }
        
        htmlContent += `</td>
                <td>${product.name}</td>
                <td>${product.sku || 'N/A'}</td>
                <td>${product.unitPrice}</td>
                <td>${product.quantity}</td>`;
        
        // Add custom column data
        if (data.customColumns && data.customColumns.length > 0) {
          data.customColumns.forEach((col: any) => {
            const customValue = product.customData?.[col.id] || 'N/A';
            let displayValue = customValue;
            
            if (col.type === 'date' && customValue && customValue !== 'N/A') {
              try {
                displayValue = new Date(customValue).toLocaleDateString();
              } catch (e) {
                displayValue = customValue;
              }
            }
            
            htmlContent += `<td>${displayValue}</td>`;
          });
        }
        
        htmlContent += `
                <td>${product.total}</td>
            </tr>`;
      });
      
      htmlContent += `
        </tbody>
    </table>`;
    }

    // Financial Summary with Fixed Layout
    if (selectedExportColumns.includes('financialSummary') || selectedExportColumns.includes('total') || selectedExportColumns.includes('commission') || selectedExportColumns.includes('profit')) {
      htmlContent += `
    <div class="summary-section">
        <div class="section-title">Financial Summary</div>
        <table>
            <colgroup>
                <col width="200">
                <col width="150">
            </colgroup>
            <thead>
                <tr>
                    <th>Metric</th>
                    <th>Amount</th>
                </tr>
            </thead>
            <tbody>`;
      
      if (selectedExportColumns.includes('total')) {
        htmlContent += `
                <tr>
                    <td><strong>Total Amount</strong></td>
                    <td><strong>${data.total}</strong></td>
                </tr>`;
      }
      
      if (selectedExportColumns.includes('commission')) {
        htmlContent += `
                <tr>
                    <td>Commission</td>
                    <td>${data.commission}</td>
                </tr>`;
      }
      
      if (selectedExportColumns.includes('profit')) {
        htmlContent += `
                <tr>
                    <td>Profit</td>
                    <td>${data.profit}</td>
                </tr>`;
      }
      
      htmlContent += `
                <tr>
                    <td>Total Products</td>
                    <td>${data.financialSummary.productCount}</td>
                </tr>
            </tbody>
        </table>
    </div>`;
    }

    htmlContent += `
    <div class="summary-section">
        <p><strong>Export Date:</strong> ${new Date().toLocaleDateString()}</p>
        <p><strong>Export Time:</strong> ${new Date().toLocaleTimeString()}</p>
        <p><em>Note: Product photos are sized to 80×60 pixels and anchored within their cells for consistent display across Excel applications.</em></p>
    </div>
</body>
</html>`;

    const blob = new Blob([htmlContent], { type: 'application/vnd.ms-excel' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sales-order-${sale?.id}-${new Date().toISOString().split('T')[0]}.xls`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const handleExport = () => {
    const data = generateOrderExport();
    if (exportFormat === 'excel') {
      downloadOrderExcel(data);
    } else {
      downloadOrderHTML(data);
    }
    setShowExportModal(false);
  };

  // -------------------------------------------------------------------------
  // Sale editing
  // -------------------------------------------------------------------------
  const handleEditSale = () => {
    if (isEditing && sale) {
      try {
        const salesStore = SalesStore.getInstance();
        salesStore.updateSale(saleId, {
          customer: editForm.customer,
          salesRep: editForm.salesRep,
          deliveryDate: editForm.deliveryDate,
          status: editForm.status,
          vendor: editForm.salesRep // keep vendor in sync
        });
      } catch (error) {
        console.error('Failed to update sale:', error);
      }
    }
    setIsEditing(!isEditing);
  };

  // -------------------------------------------------------------------------
  // Product editing
  // -------------------------------------------------------------------------
  const handleEditProduct = (productIndex: number) => {
    if (!sale) return;

    if (editingProduct === `product-${productIndex}`) {
      // Save changes
      try {
        const salesStore = SalesStore.getInstance();
        const updatedProduct = {
          ...productEditForm,
          sku:
            productEditForm.sku ||
            `SKU-${Math.random()
              .toString(36)
              .substr(2, 6)
              .toUpperCase()}`
        };
        salesStore.updateProduct(saleId, productIndex, updatedProduct);
      } catch (error) {
        console.error('Failed to update product:', error);
      }
      setEditingProduct(null);
    } else {
      // Start editing
      const p = sale.products[productIndex];
      setProductEditForm({
        ...p,
        sku:
          p.sku ||
          `SKU-${Math.random()
            .toString(36)
            .substr(2, 6)
            .toUpperCase()}`
      });
      setEditingProduct(`product-${productIndex}`);
    }
  };

  // -------------------------------------------------------------------------
  // Photo handling
  // -------------------------------------------------------------------------
  const handlePhotoUpload = (
    productIndex: number,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = ev => {
      const url = ev.target?.result as string;
      try {
        const salesStore = SalesStore.getInstance();
        salesStore.updateProduct(saleId, productIndex, { photo: url });
      } catch (error) {
        console.error('Failed to upload photo:', error);
      }
    };
    reader.readAsDataURL(file);
  };

  // -------------------------------------------------------------------------
  // Helpers
  // -------------------------------------------------------------------------
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Processing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Shipped':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'Delivered':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Confirmed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // -------------------------------------------------------------------------
  // WeChat Integration functionality
  // -------------------------------------------------------------------------
  const handleSendToWeChat = async () => {
    if (!sale || !wechatConfig.recipientOpenId) return;

    setIsSendingWeChat(true);
    setWechatSendResult(null);

    try {
      // Generate invoice data for WeChat
      const wechatInvoiceData = {
        orderId: sale.id,
        customer: sale.customer,
        salesRep: sale.salesRep || sale.vendor,
        total: sale.total,
        status: sale.status,
        products: sale.products.map(product => ({
          name: product.name,
          quantity: product.quantity,
          price: product.unitPrice,
          total: product.total
        })),
        recipientOpenId: wechatConfig.recipientOpenId,
        recipientName: wechatConfig.recipientName,
        customMessage: wechatConfig.message,
        sendType: wechatConfig.sendType
      };

      // Simulate WeChat API call (replace with actual WeChat API integration)
      const response = await simulateWeChatSend(wechatInvoiceData);
      
      setWechatSendResult({
        success: response.success,
        message: response.message
      });

      if (response.success) {
        // Auto-close modal after success
        setTimeout(() => {
          setShowWeChatModal(false);
          setWechatConfig({
            recipientOpenId: '',
            recipientName: '',
            message: '',
            sendType: 'template'
          });
        }, 2000);
      }

    } catch (error) {
      console.error('WeChat send error:', error);
      setWechatSendResult({
        success: false,
        message: 'Failed to send invoice to WeChat. Please try again.'
      });
    } finally {
      setIsSendingWeChat(false);
    }
  };

  // Simulate WeChat API integration (replace with actual WeChat API)
  const simulateWeChatSend = async (data: any): Promise<{success: boolean, message: string}> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Simulate success/failure based on data validation
    if (data.recipientOpenId && data.orderId) {
      return {
        success: true,
        message: `Invoice ${data.orderId} sent successfully to ${data.recipientName || 'WeChat user'}`
      };
    } else {
      return {
        success: false,
        message: 'Invalid recipient information. Please check WeChat OpenID.'
      };
    }
  };

  const generateWeChatInvoiceTemplate = () => {
    if (!sale) return '';

    return `📋 *Invoice ${sale.id}*

👤 Customer: ${sale.customer}
👨‍💼 Sales Rep: ${sale.salesRep || sale.vendor}
📅 Date: ${new Date(sale.salesDate).toLocaleDateString()}
📦 Status: ${sale.status}

*Products:*
${sale.products.map((product, index) => 
  `${index + 1}. ${product.name}
   Qty: ${product.quantity} × ${product.unitPrice} = ${product.total}`
).join('\n')}

💰 *Total: ${sale.total}*
💼 Commission: ${sale.commission}

${wechatConfig.message ? `\n📝 Note: ${wechatConfig.message}` : ''}

Thank you for your business! 🙏`;
  };

  // -------------------------------------------------------------------------
  // WhatsApp Integration functionality
  // -------------------------------------------------------------------------
  const handleSendToWhatsApp = async () => {
    if (!sale || !whatsappConfig.phoneNumber) return;

    setIsSendingWhatsApp(true);
    setWhatsappSendResult(null);

    try {
      // Generate invoice data for WhatsApp
      const whatsappInvoiceData = {
        orderId: sale.id,
        customer: sale.customer,
        salesRep: sale.salesRep || sale.vendor,
        total: sale.total,
        status: sale.status,
        products: sale.products.map(product => ({
          name: product.name,
          quantity: product.quantity,
          price: product.unitPrice,
          total: product.total
        })),
        phoneNumber: whatsappConfig.phoneNumber,
        recipientName: whatsappConfig.recipientName,
        customMessage: whatsappConfig.message,
        sendType: whatsappConfig.sendType
      };

      // Simulate WhatsApp API call (replace with actual WhatsApp Business API integration)
      const response = await simulateWhatsAppSend(whatsappInvoiceData);
      
      setWhatsappSendResult({
        success: response.success,
        message: response.message
      });

      if (response.success) {
        // Auto-close modal after success
        setTimeout(() => {
          setShowWhatsAppModal(false);
          setWhatsappConfig({
            phoneNumber: '',
            recipientName: '',
            message: '',
            sendType: 'template'
          });
        }, 2000);
      }

    } catch (error) {
      console.error('WhatsApp send error:', error);
      setWhatsappSendResult({
        success: false,
        message: 'Failed to send invoice to WhatsApp. Please try again.'
      });
    } finally {
      setIsSendingWhatsApp(false);
    }
  };

  // Simulate WhatsApp API integration (replace with actual WhatsApp Business API)
  const simulateWhatsAppSend = async (data: any): Promise<{success: boolean, message: string}> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Simulate success/failure based on data validation
    if (data.phoneNumber && data.orderId) {
      return {
        success: true,
        message: `Invoice ${data.orderId} sent successfully to ${data.phoneNumber}`
      };
    } else {
      return {
        success: false,
        message: 'Invalid phone number. Please check WhatsApp number format.'
      };
    }
  };

  const generateWhatsAppInvoiceTemplate = () => {
    if (!sale) return '';

    return `📋 *Invoice ${sale.id}*

👤 Customer: ${sale.customer}
👨‍💼 Sales Rep: ${sale.salesRep || sale.vendor}
📅 Date: ${new Date(sale.salesDate).toLocaleDateString()}
📦 Status: ${sale.status}

*Products:*
${sale.products.map((product, index) => 
  `${index + 1}. ${product.name}
   Qty: ${product.quantity} × ${product.unitPrice} = ${product.total}`
).join('\n')}

💰 *Total: ${sale.total}*
💼 Commission: ${sale.commission}

${whatsappConfig.message ? `\n📝 Note: ${whatsappConfig.message}` : ''}

Thank you for your business! 🙏`;
  };

  // -------------------------------------------------------------------------
  // Rendering
  // -------------------------------------------------------------------------
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
        <main
          className={`transition-all duration-300 p-6 ${sidebarOpen ? 'pl-64' : 'pl-16'}`}
        >
          <div className="max-w-7xl mx-auto animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4" />
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-8" />
            <div className="space-y-4">
              <div className="h-32 bg-gray-200 rounded" />
              <div className="h-48 bg-gray-200 rounded" />
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (!sale) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
        <main
          className={`transition-all duration-300 p-6 ${sidebarOpen ? 'pl-64' : 'pl-16'}`}
        >
          <div className="max-w-7xl mx-auto">
            <div className="text-center py-16">
              <i className="ri-file-search-line w-16 h-16 flex items-center justify-center mx-auto text-gray-300 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Sales record not found
              </h3>
              <p className="text-sm text-gray-500 mb-4">
                The sales record you're looking for doesn't exist.
              </p>
              <Link
                href="/sales"
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700"
              >
                Back to Sales
              </Link>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

      <main
        className={`transition-all duration-300 p-6 ${sidebarOpen ? 'pl-64' : 'pl-16'}`}
      >
        <div className="max-w-7xl mx-auto">
          {/* Header section */}
          <div className="mb-6">
            <div className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
              <Link href="/sales" className="hover:text-gray-700">
                Sales
              </Link>
              <i className="ri-arrow-right-s-line w-4 h-4 flex items-center justify-center" />
              <span className="text-gray-900 font-medium">{sale.id}</span>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Sales Order {sale.id}
                </h1>
                <p className="text-gray-600">
                  View and manage sales order details
                </p>
              </div>
              <div className="flex items-center space-x-3">
                <div className="relative group">
                  <button className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100">
                    <i className="ri-download-line w-4 h-4 flex items-center justify-center" />
                    <span>Export Order</span>
                    <i className="ri-arrow-down-s-line w-3 h-3 flex items-center justify-center" />
                  </button>
                  
                  {/* Export Dropdown */}
                  <div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-xl z-50 min-w-[200px] py-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                    <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100">
                      Export Options
                    </div>
                    
                    <button
                      onClick={() => setShowExportModal(true)}
                      className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 cursor-pointer"
                    >
                      <i className="ri-file-text-line w-4 h-4 flex items-center justify-center text-blue-600"></i>
                      <div className="text-left">
                        <div className="font-medium">Custom Export</div>
                        <div className="text-xs text-gray-500">Choose format and columns</div>
                      </div>
                    </button>

                    <button
                      onClick={() => {
                        const data = generateOrderExport();
                        downloadOrderHTML(data);
                      }}
                      className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 cursor-pointer"
                    >
                      <i className="ri-file-download-line w-4 h-4 flex items-center justify-center text-green-600"></i>
                      <div className="text-left">
                        <div className="font-medium">Quick HTML Export</div>
                        <div className="text-xs text-gray-500">Professional report with all data</div>
                      </div>
                    </button>

                    <button
                      onClick={() => {
                        const data = generateOrderExport();
                        downloadOrderExcel(data);
                      }}
                      className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-700 cursor-pointer"
                    >
                      <i className="ri-file-excel-2-line w-4 h-4 flex items-center justify-center text-purple-600"></i>
                      <div className="text-left">
                        <div className="font-medium">Quick Excel Export</div>
                        <div className="text-xs text-gray-500">Spreadsheet with all data</div>
                      </div>
                    </button>
                  </div>
                </div>

                <button
                  onClick={() => setShowColumnModal(true)}
                  className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-purple-600 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100"
                >
                  <i className="ri-add-column-line w-4 h-4 flex items-center justify-center" />
                  <span>Add Column</span>
                </button>

                <span
                  className={`inline-flex items-center px-3 py-2 rounded-full text-sm font-medium border ${getStatusColor(
                    sale.status
                  )}`}
                >
                  <div
                    className={`w-2 h-2 rounded-full mr-2 ${sale.status === 'Completed'
                      ? 'bg-green-500'
                      : sale.status === 'Processing'
                      ? 'bg-blue-500'
                      : sale.status === 'Shipped'
                      ? 'bg-purple-500'
                      : sale.status === 'Delivered'
                      ? 'bg-gray-500'
                      : sale.status === 'Pending'
                      ? 'bg-yellow-500'
                      : sale.status === 'Confirmed'
                      ? 'bg-blue-500'
                      : 'bg-red-500'}`}
                  ></div>
                  {sale.status}
                </span>

                <button
                  onClick={handleEditSale}
                  className={`px-4 py-2 text-sm font-medium rounded-lg border whitespace-nowrap ${isEditing
                    ? 'bg-green-600 text-white border-green-600 hover:bg-green-700'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'}`}
                >
                  {isEditing ? (
                    <>
                      <i className="ri-save-line w-4 h-4 flex items-center justify-center mr-2 inline" />
                      Save Changes
                    </>
                  ) : (
                    <>
                      <i className="ri-edit-line w-4 h-4 flex items-center justify-center mr-2 inline" />
                      Edit Order
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Main layout */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left side (order info + products) */}
            <div className="lg:col-span-2 space-y-6">
              {/* Order Information */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Order Information
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Customer */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Customer
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={editForm.customer}
                        onChange={e =>
                          setEditForm(prev => ({
                            ...prev,
                            customer: e.target.value
                          }))
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      />
                    ) : (
                      <div className="text-sm text-gray-900 font-medium">
                        {sale.customer}
                      </div>
                    )}
                  </div>

                  {/* Sales Rep */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Sales Representative
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={editForm.salesRep}
                        onChange={e =>
                          setEditForm(prev => ({
                            ...prev,
                            salesRep: e.target.value
                          }))
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      />
                    ) : (
                      <div className="text-sm text-gray-900 font-medium">
                        {sale.salesRep || sale.vendor}
                      </div>
                    )}
                  </div>

                  {/* Delivery Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Delivery Date
                    </label>
                    {isEditing ? (
                      <input
                        type="date"
                        value={editForm.deliveryDate}
                        onChange={e =>
                          setEditForm(prev => ({
                            ...prev,
                            deliveryDate: e.target.value
                          }))
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      />
                    ) : (
                      <div className="text-sm text-gray-900">
                        {sale.deliveryDate || 'Not specified'}
                      </div>
                    )}
                  </div>

                  {/* Status */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Status
                    </label>
                    {isEditing ? (
                      <select
                        value={editForm.status}
                        onChange={e =>
                          setEditForm(prev => ({
                            ...prev,
                            status: e.target.value
                          }))
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-8"
                      >
                        <option value="Pending">Pending</option>
                        <option value="Confirmed">Confirmed</option>
                        <option value="Processing">Processing</option>
                        <option value="Shipped">Shipped</option>
                        <option value="Delivered">Delivered</option>
                        <option value="Completed">Completed</option>
                        <option value="Cancelled">Cancelled</option>
                      </select>
                    ) : (
                      <div
                        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(
                          sale.status
                        )}`}
                      >
                        <div
                          className={`w-2 h-2 rounded-full mr-2 ${sale.status === 'Completed'
                            ? 'bg-green-500'
                            : sale.status === 'Processing'
                            ? 'bg-blue-500'
                            : sale.status === 'Shipped'
                            ? 'bg-purple-500'
                            : sale.status === 'Delivered'
                            ? 'bg-gray-500'
                            : sale.status === 'Pending'
                            ? 'bg-yellow-500'
                            : sale.status === 'Confirmed'
                            ? 'bg-blue-500'
                            : 'bg-red-500'}`}
                        ></div>
                        {sale.status}
                      </div>
                    )}
                  </div>
                </div>

                {/* Order meta */}
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Order Date */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Order Date
                      </label>
                      <div className="text-sm text-gray-900">
                        {new Date(sale.salesDate).toLocaleDateString()}
                      </div>
                    </div>

                    {/* Order ID */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Order ID
                      </label>
                      <div className="text-sm text-gray-900 font-mono">
                        {sale.id}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Products Table */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Products
                  </h3>
                  <button
                    onClick={() => setShowAddProductModal(true)}
                    className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap"
                  >
                    <i className="ri-add-line w-4 h-4 flex items-center justify-center"></i>
                    <span>Add Product</span>
                  </button>
                </div>

                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Photo
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Product
                        </th>
                        {/* Conditionally render SKU column */}
                        {sale.products.some(
                          p =>
                            p.sku &&
                            p.sku.trim() !== '' &&
                            !p.sku.startsWith('SKU-')
                        ) && (
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            SKU
                          </th>
                        )}
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Unit Price
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Quantity
                        </th>
                        {/* Custom columns */}
                        {sale.customColumns?.map(col => (
                          <th
                            key={col.id}
                            className="px-6 py-3 text-left text-xs font-medium text-purple-600 uppercase tracking-wider"
                          >
                            <div className="flex items-center justify-between">
                              <span>{col.name}</span>
                              <button
                                onClick={() => removeCustomColumn(col.id)}
                                className="text-red-400 hover:text-red-600"
                              >
                                <i className="ri-close-line w-3 h-3 flex items-center justify-center" />
                              </button>
                            </div>
                          </th>
                        ))}
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                        <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {sale.products.map((product, idx) => (
                        <tr key={idx} className="hover:bg-gray-50">
                          {/* Photo */}
                          <td className="px-6 py-4">
                            <div className="relative group">
                              {product.photo ? (
                                <div className="relative">
                                  <img
                                    src={product.photo}
                                    alt={product.name}
                                    className="w-16 h-12 object-cover rounded-lg border border-gray-200"
                                  />
                                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity rounded-lg cursor-pointer">
                                    <label
                                      htmlFor={`photo-${idx}`}
                                      className="text-white text-xs"
                                    >
                                      <i className="ri-camera-line w-4 h-4 flex items-center justify-center" />
                                    </label>
                                  </div>
                                </div>
                              ) : (
                                <div className="w-16 h-12 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center group-hover:border-gray-400 transition-colors">
                                  <label
                                    htmlFor={`photo-${idx}`}
                                    className="cursor-pointer"
                                  >
                                    <i className="ri-image-add-line w-5 h-5 flex items-center justify-center text-gray-400 group-hover:text-gray-600" />
                                  </label>
                                </div>
                              )}
                              <input
                                id={`photo-${idx}`}
                                type="file"
                                accept="image/*"
                                onChange={e => handlePhotoUpload(idx, e)}
                                className="hidden"
                              />
                            </div>
                          </td>

                          {/* Product name */}
                          <td className="px-6 py-4">
                            {editingProduct === `product-${idx}` ? (
                              <input
                                type="text"
                                value={productEditForm.name}
                                onChange={e =>
                                  setProductEditForm(prev => ({
                                    ...prev,
                                    name: e.target.value
                                  }))
                                }
                                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                              />
                            ) : (
                              <div className="text-sm font-medium text-gray-900">
                                {product.name}
                              </div>
                            )}
                          </td>

                          {/* SKU (conditionally) */}
                          {sale.products.some(
                            p =>
                              p.sku &&
                              p.sku.trim() !== '' &&
                              !p.sku.startsWith('SKU-')
                          ) && (
                            <td className="px-6 py-4">
                              {editingProduct === `product-${idx}` ? (
                                <input
                                  type="text"
                                  value={productEditForm.sku || ''}
                                  onChange={e =>
                                    setProductEditForm(prev => ({
                                      ...prev,
                                      sku: e.target.value
                                    }))
                                  }
                                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm font-mono"
                                  placeholder="Enter SKU"
                                />
                              ) : (
                                <div className="text-sm text-gray-900 font-mono bg-gray-50 px-2 py-1 rounded">
                                  {product.sku && product.sku.trim() !== '' && !product.sku.startsWith('SKU-')
                                    ? product.sku
                                    : 'N/A'}
                                </div>
                              )}
                            </td>
                          )}

                          {/* Unit Price */}
                          <td className="px-6 py-4">
                            {editingProduct === `product-${idx}` ? (
                              <input
                                type="text"
                                value={productEditForm.unitPrice}
                                onChange={e =>
                                  setProductEditForm(prev => ({
                                    ...prev,
                                    unitPrice: e.target.value
                                  }))
                                }
                                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                              />
                            ) : (
                              <div className="text-sm text-gray-900">
                                {product.unitPrice}
                              </div>
                            )}
                          </td>

                          {/* Quantity */}
                          <td className="px-6 py-4">
                            {editingProduct === `product-${idx}` ? (
                              <input
                                type="number"
                                min="1"
                                value={productEditForm.quantity}
                                onChange={e =>
                                  setProductEditForm(prev => ({
                                    ...prev,
                                    quantity: parseInt(e.target.value) || 0
                                  }))
                                }
                                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                              />
                            ) : (
                              <div className="text-sm text-gray-900">
                                {product.quantity}
                              </div>
                            )}
                          </td>

                          {/* Custom column cells */}
                          {sale.customColumns?.map(col => (
                            <td key={col.id} className="px-6 py-4">
                              {editingProduct === `product-${idx}` ? (
                                col.type === 'select' ? (
                                  <select
                                    value={productEditForm.customData?.[col.id] || ''}
                                    onChange={e =>
                                      setProductEditForm(prev => ({
                                        ...prev,
                                        customData: {
                                          ...prev.customData,
                                          [col.id]: e.target.value
                                        }
                                      }))
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm pr-8"
                                  >
                                    <option value="">Select...</option>
                                    {col.options?.map((opt, i) => (
                                      <option key={i} value={opt}>
                                        {opt}
                                      </option>
                                    ))}
                                  </select>
                                ) : (
                                  <input
                                    type={col.type}
                                    value={productEditForm.customData?.[col.id] || ''}
                                    onChange={e =>
                                      setProductEditForm(prev => ({
                                        ...prev,
                                        customData: {
                                          ...prev.customData,
                                          [col.id]: e.target.value
                                        }
                                      }))
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                                  />
                                )
                              ) : (
                                <div className="text-sm text-purple-700 bg-purple-50 px-2 py-1 rounded">
                                  {product.customData?.[col.id] ?? 'N/A'}
                                </div>
                              )}
                            </td>
                          ))}

                          {/* Total */}
                          <td className="px-6 py-4">
                            <div className="text-sm font-semibold text-green-600">
                              {product.total}
                            </div>
                          </td>

                          {/* Actions */}
                          <td className="px-6 py-4 text-center">
                            <button
                              onClick={() => handleEditProduct(idx)}
                              className={`px-3 py-1.5 text-xs font-medium rounded whitespace-nowrap ${editingProduct === `product-${idx}`
                                ? 'bg-green-600 text-white hover:bg-green-700'
                                : 'bg-blue-600 text-white hover:bg-blue-700'}`}
                            >
                              {editingProduct === `product-${idx}` ? (
                                <>
                                  <i className="ri-save-line w-3 h-3 flex items-center justify-center mr-1 inline" />
                                  Save
                                </>
                              ) : (
                                <>
                                  <i className="ri-edit-line w-3 h-3 flex items-center justify-center mr-1 inline" />
                                  Edit
                                </>
                              )}
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Right side (summary + actions) */}
            <div className="space-y-6">
              {/* Financial Summary */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Financial Summary
                </h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm text-gray-600">Total Amount</span>
                    <span className="text-lg font-bold text-gray-900">{sale.total}</span>
                  </div>
                  <div className="flex justify-between items-center py-2">
                    <span className="text-sm text-gray-600">Net Amount</span>
                    <span className="text-lg font-bold text-green-600">{sale.total}</span>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Quick Actions
                </h3>
                <div className="space-y-3">
                  <button className="w-full px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100">
                    <i className="ri-printer-line w-4 h-4 flex items-center justify-center mr-2 inline" />
                    Print Invoice
                  </button>
                  <button className="w-full px-4 py-2 text-sm font-medium text-green-600 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100">
                    <i className="ri-download-line w-4 h-4 flex items-center justify-center mr-2 inline" />
                    Download PDF
                  </button>
                  <button 
                    onClick={() => setShowWeChatModal(true)}
                    className="w-full px-4 py-2 text-sm font-medium text-green-600 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 cursor-pointer whitespace-nowrap"
                  >
                    <i className="ri-wechat-line w-4 h-4 flex items-center justify-center mr-2 inline" />
                    Send to WeChat
                  </button>
                  <button 
                    onClick={() => setShowWhatsAppModal(true)}
                    className="w-full px-4 py-2 text-sm font-medium text-green-600 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 cursor-pointer whitespace-nowrap"
                  >
                    <i className="ri-whatsapp-line w-4 h-4 flex items-center justify-center mr-2 inline" />
                    Send to WhatsApp
                  </button>
                  <button className="w-full px-4 py-2 text-sm font-medium text-purple-600 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100">
                    <i className="ri-mail-line w-4 h-4 flex items-center justify-center mr-2 inline" />
                    Email Customer
                  </button>
                </div>
              </div>

              {/* Order Timeline */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Order Timeline
                </h3>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        Order Created
                      </div>
                      <div className="text-xs text-gray-500">
                        {new Date(sale.salesDate).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  {sale.status === 'Confirmed' && (
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          Order Confirmed
                        </div>
                        <div className="text-xs text-gray-500">Today</div>
                      </div>
                    </div>
                  )}
                  {(sale.status === 'Processing' ||
                    sale.status === 'Shipped' ||
                    sale.status === 'Delivered' ||
                    sale.status === 'Completed') && (
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          Processing Started
                        </div>
                        <div className="text-xs text-gray-500">Today</div>
                      </div>
                    </div>
                  )}
                  {(sale.status === 'Shipped' ||
                    sale.status === 'Delivered' ||
                    sale.status === 'Completed') && (
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-purple-500 rounded-full mt-2" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          Order Shipped
                        </div>
                        <div className="text-xs text-gray-500">Today</div>
                      </div>
                    </div>
                  )}
                  {(sale.status === 'Delivered' ||
                    sale.status === 'Completed') && (
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-gray-500 rounded-full mt-2" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          Order Delivered
                        </div>
                        <div className="text-xs text-gray-500">
                          {sale.deliveryDate || 'Today'}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Add New Product Modal */}
      {showAddProductModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Add New Product</h3>
              <button
                onClick={() => setShowAddProductModal(false)}
                className="text-gray-400 hover:text-gray-600 cursor-pointer"
              >
                <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
              </button>
            </div>

            <div className="space-y-4">
              {/* Product Photo */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Product Photo</label>
                <div className="flex items-center space-x-4">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleNewProductPhotoChange}
                    className="hidden"
                    id="new-product-photo"
                  />
                  <label
                    htmlFor="new-product-photo"
                    className="w-20 h-20 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50 cursor-pointer hover:border-gray-400 hover:bg-gray-100 transition-colors"
                  >
                    {newProduct.photoPreview ? (
                      <img 
                        src={newProduct.photoPreview} 
                        alt="Preview" 
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <i className="ri-image-add-line w-8 h-8 flex items-center justify-center text-gray-400"></i>
                    )}
                  </label>
                  {newProduct.photo && (
                    <button
                      type="button"
                      onClick={removeNewProductPhoto}
                      className="px-3 py-1.5 text-xs font-medium text-red-600 bg-white border border-red-200 rounded hover:bg-red-50 cursor-pointer"
                    >
                      Remove
                    </button>
                  )}
                </div>
              </div>

              {/* Product Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Product Name *</label>
                <input
                  type="text"
                  required
                  value={newProduct.name}
                  onChange={(e) => setNewProduct(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter product name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
              </div>

              {/* SKU */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">SKU</label>
                <input
                  type="text"
                  value={newProduct.sku}
                  onChange={(e) => setNewProduct(prev => ({ ...prev, sku: e.target.value }))}
                  placeholder="Enter SKU (optional)"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
              </div>

              {/* Price and Quantity */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Unit Price *</label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">$</span>
                    <input
                      type="number"
                      required
                      min="0"
                      step="0.01"
                      value={newProduct.unitPrice}
                      onChange={(e) => setNewProduct(prev => ({ ...prev, unitPrice: e.target.value }))}
                      placeholder="0.00"
                      className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Quantity *</label>
                  <input
                    type="number"
                    required
                    min="1"
                    value={newProduct.quantity}
                    onChange={(e) => setNewProduct(prev => ({ ...prev, quantity: parseInt(e.target.value) || 1 }))}
                    placeholder="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                </div>
              </div>

              {/* Custom Fields */}
              {sale?.customColumns && sale.customColumns.length > 0 && (
                <div className="border-t border-gray-200 pt-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Custom Fields</h4>
                  <div className="space-y-3">
                    {sale.customColumns.map(col => (
                      <div key={col.id}>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {col.name} {col.required && <span className="text-red-500">*</span>}
                        </label>
                        {col.type === 'select' ? (
                          <select
                            required={col.required}
                            value={newProduct.customData[col.id] || ''}
                            onChange={(e) => handleNewProductCustomDataChange(col.id, e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm pr-8"
                          >
                            <option value="">Select...</option>
                            {col.options?.map((option, i) => (
                              <option key={i} value={option}>{option}</option>
                            ))}
                          </select>
                        ) : (
                          <input
                            type={col.type}
                            required={col.required}
                            value={newProduct.customData[col.id] || ''}
                            onChange={(e) => handleNewProductCustomDataChange(col.id, e.target.value)}
                            placeholder={`Enter ${col.name.toLowerCase()}`}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                          />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Total Preview */}
              {newProduct.unitPrice && newProduct.quantity && (
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-700">Total Amount:</span>
                    <span className="text-lg font-bold text-green-600">
                      ${((parseFloat(newProduct.unitPrice) || 0) * (newProduct.quantity || 1)).toFixed(2)}</span>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowAddProductModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleAddProduct}
                  disabled={!newProduct.name.trim() || !newProduct.unitPrice}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer whitespace-nowrap flex items-center space-x-2"
                >
                  <i className="ri-add-line w-4 h-4 flex items-center justify-center"></i>
                  <span>Add Product</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Custom Column Modal */}
      {showColumnModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-lg mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Add Custom Column
              </h3>
              <button
                onClick={() => setShowColumnModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <i className="ri-close-line w-6 h-6 flex items-center justify-center" />
              </button>
            </div>

            <div className="space-y-4">
              {/* Column name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Column Name
                </label>
                <input
                  type="text"
                  required
                  value={newColumn.name}
                  onChange={e =>
                    setNewColumn(prev => ({ ...prev, name: e.target.value }))
                  }
                  placeholder="Enter column name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
              </div>

              {/* Data type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Data Type
                </label>
                <select
                  value={newColumn.type}
                  onChange={e =>
                    setNewColumn(prev => ({
                      ...prev,
                      type: e.target.value as any
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-8"
                >
                  <option value="text">Text</option>
                  <option value="number">Number</option>
                  <option value="date">Date</option>
                  <option value="select">Dropdown</option>
                </select>
              </div>

              {/* Options for select */}
              {newColumn.type === 'select' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Options
                  </label>
                  <div className="space-y-2">
                    {newColumn.options.map((opt, i) => (
                      <div key={i} className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={opt}
                          onChange={e => updateSelectOption(i, e.target.value)}
                          placeholder={`Option ${i + 1}`}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                        />
                        {newColumn.options.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeSelectOption(i)}
                            className="p-2 text-red-500 hover:text-red-700"
                          >
                            <i className="ri-close-line w-4 h-4 flex items-center justify-center" />
                          </button>
                        )}
                      </div>
                    ))}
                    <button
                      type="button"
                      onClick={addSelectOption}
                      className="flex items-center space-x-2 px-3 py-2 text-sm text-blue-600 hover:text-blue-800"
                    >
                      <i className="ri-add-line w-3 h-3 flex items-center justify-center" />
                      <span>Add Option</span>
                    </button>
                  </div>
                </div>
              )}

              {/* Required toggle */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="required"
                  checked={newColumn.required}
                  onChange={e =>
                    setNewColumn(prev => ({
                      ...prev,
                      required: e.target.checked
                    }))
                  }
                  className="rounded border-gray-300 w-4 h-4 text-blue-600 focus:ring-blue-500 cursor-pointer"
                />
                <label htmlFor="required" className="ml-2 text-sm text-gray-700">
                  Required field
                </label>
              </div>

              {/* Action buttons */}
              <div className="flex items-center justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowColumnModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={addCustomColumn}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700"
                >
                  Add Column
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Export Modal */}
      {showExportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Export Sales Order
              </h3>
              <button
                onClick={() => setShowExportModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <i className="ri-close-line w-6 h-6 flex items-center justify-center" />
              </button>
            </div>

            <div className="space-y-6">
              {/* Export format */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Export Format
                </label>
                <div className="space-y-3">
                  <label className="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                    <input
                      type="radio"
                      value="html"
                      checked={exportFormat === 'html'}
                      onChange={e => setExportFormat(e.target.value as any)}
                      className="mt-1 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 mb-1">
                        HTML Report
                      </div>
                      <div className="space-y-1">
                        <div className="text-sm text-green-600 flex items-center">
                          <i className="ri-check-line w-4 h-4 flex items-center justify-center mr-1" />
                          Professional formatting with complete order details
                        </div>
                        <div className="text-sm text-green-600 flex items-center">
                          <i className="ri-check-line w-4 h-4 flex items-center justify-center mr-1" />
                          Includes financial summary and product breakdown
                        </div>
                        <div className="text-sm text-green-600 flex items-center">
                          <i className="ri-check-line w-4 h-4 flex items-center justify-center mr-1" />
                          Can be converted to PDF or printed
                        </div>
                      </div>
                      <div className="mt-2 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded font-medium">
                        Recommended for Reports
                      </div>
                    </div>
                  </label>

                  <label className="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                    <input
                      type="radio"
                      value="excel"
                      checked={exportFormat === 'excel'}
                      onChange={e => setExportFormat(e.target.value as any)}
                      className="mt-1 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 mb-1">
                        Excel Format
                      </div>
                      <div className="space-y-1">
                        <div className="text-sm text-gray-500 flex items-center">
                          <i className="ri-bar-chart-line w-4 h-4 flex items-center justify-center mr-1" />
                          Perfect for data analysis and calculations
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <i className="ri-calculator-line w-4 h-4 flex items-center justify-center mr-1" />
                          Structured tables with order and product data
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <i className="ri-table-line w-4 h-4 flex items-center justify-center mr-1" />
                          Includes custom columns and financial metrics
                        </div>
                      </div>
                    </div>
                  </label>
                </div>
              </div>

              {/* Column selection */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <label className="block text-sm font-medium text-gray-700">
                    Select Data Sections to Export
                  </label>
                  <div className="text-xs text-gray-500">
                    {selectedExportColumns.length} of{' '}
                    {getAvailableExportColumns().length} selected
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-2 max-h-60 overflow-y-auto border border-gray-200 rounded-lg p-3 bg-gray-50">
                  {getAvailableExportColumns().map(col => (
                    <label
                      key={col.id}
                      className="flex items-center space-x-2 py-2 px-3 bg-white rounded border border-gray-100 hover:border-gray-300 cursor-pointer"
                    >
                      <input
                        type="checkbox"
                        checked={selectedExportColumns.includes(col.id)}
                        onChange={e => {
                          if (e.target.checked) {
                            setSelectedExportColumns(prev => [
                              ...prev,
                              col.id
                            ]);
                          } else {
                            setSelectedExportColumns(prev =>
                              prev.filter(id => id !== col.id)
                            );
                          }
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 w-4 h-4"
                      />
                      <span className="text-sm text-gray-700 truncate">
                        {col.name}
                      </span>
                    </label>
                  ))}
                </div>
                <div className="flex items-center space-x-2 mt-3">
                  <button
                    type="button"
                    onClick={() =>
                      setSelectedExportColumns(
                        getAvailableExportColumns().map(c => c.id)
                      )
                    }
                    className="text-xs text-blue-600 hover:text-blue-800"
                  >
                    Select All
                  </button>
                  <span className="text-gray-300">|</span>
                  <button
                    type="button"
                    onClick={() => setSelectedExportColumns([])}
                    className="text-xs text-red-600 hover:text-red-800"
                  >
                    Clear All
                  </button>
                  <span className="text-gray-300">|</span>
                  <button
                    type="button"
                    onClick={() =>
                      setSelectedExportColumns([
                        'orderInfo',
                        'customer',
                        'products',
                        'total',
                        'status'
                      ])
                    }
                    className="text-xs text-green-600 hover:text-green-800"
                  >
                    Essential Only
                  </button>
                </div>
              </div>

              {/* Info block */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-sm text-blue-700">
                  <strong>Export Preview:</strong> This will export the complete sales order ({sale?.id}) with the selected data sections including products, financial summary, and custom fields.
                </p>
              </div>

              {/* Action buttons */}
              <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowExportModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleExport}
                  disabled={selectedExportColumns.length === 0}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
                >
                  <i className="ri-download-line w-4 h-4 flex items-center justify-center" />
                  <span>Export {exportFormat === 'html' ? 'HTML' : 'Excel'}</span>
                  {selectedExportColumns.length > 0 && (
                    <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                      {selectedExportColumns.length}
                    </span>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* WeChat Send Modal */}
      {showWeChatModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <i className="ri-wechat-line w-6 h-6 flex items-center justify-center text-green-600"></i>
                <h3 className="text-lg font-semibold text-gray-900">Send Invoice to WeChat</h3>
              </div>
              <button
                onClick={() => setShowWeChatModal(false)}
                className="text-gray-400 hover:text-gray-600 cursor-pointer"
              >
                <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
              </button>
            </div>

            <div className="space-y-6">
              {/* Send Type Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">Send Method</label>
                <div className="grid grid-cols-2 gap-4">
                  <label className="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                    <input
                      type="radio"
                      value="template"
                      checked={wechatConfig.sendType === 'template'}
                      onChange={(e) => setWechatConfig(prev => ({ ...prev, sendType: e.target.value as any }))}
                      className="mt-1 w-4 h-4 text-green-600 border-gray-300 focus:ring-green-500"
                    />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 mb-1">Template Message</div>
                      <div className="text-sm text-gray-500">Send as a formatted template message with invoice details</div>
                      <div className="mt-2 text-xs text-green-600 bg-green-50 px-2 py-1 rounded font-medium">Recommended</div>
                    </div>
                  </label>

                  <label className="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                    <input
                      type="radio"
                      value="direct"
                      checked={wechatConfig.sendType === 'direct'}
                      onChange={(e) => setWechatConfig(prev => ({ ...prev, sendType: e.target.value as any }))}
                      className="mt-1 w-4 h-4 text-green-600 border-gray-300 focus:ring-green-500"
                    />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 mb-1">Direct Message</div>
                      <div className="text-sm text-gray-500">Send as a text message with custom content</div>
                    </div>
                  </label>
                </div>
              </div>

              {/* Recipient Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">WeChat OpenID *</label>
                  <input
                    type="text"
                    required
                    value={wechatConfig.recipientOpenId}
                    onChange={(e) => setWechatConfig(prev => ({ ...prev, recipientOpenId: e.target.value }))}
                    placeholder="Enter WeChat OpenID"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                  />
                  <p className="mt-1 text-xs text-gray-500">The unique WeChat OpenID of the recipient</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Recipient Name</label>
                  <input
                    type="text"
                    value={wechatConfig.recipientName}
                    onChange={(e) => setWechatConfig(prev => ({ ...prev, recipientName: e.target.value }))}
                    placeholder="Enter recipient name (optional)"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                  />
                  <p className="mt-1 text-xs text-gray-500">Display name for the recipient</p>
                </div>
              </div>

              {/* Custom Message */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Additional Message</label>
                <textarea
                  rows={3}
                  value={wechatConfig.message}
                  onChange={(e) => setWechatConfig(prev => ({ ...prev, message: e.target.value }))}
                  placeholder="Add a custom message to include with the invoice (optional)"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm resize-none"
                  maxLength={500}
                />
                <div className="mt-1 text-xs text-gray-500 text-right">
                  {wechatConfig.message.length}/500 characters
                </div>
              </div>

              {/* Invoice Preview */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Invoice Preview</label>
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-60 overflow-y-auto">
                  <pre className="text-sm text-gray-700 whitespace-pre-wrap font-mono">
                    {generateWeChatInvoiceTemplate()}
                  </pre>
                </div>
              </div>

              {/* Send Result */}
              {wechatSendResult && (
                <div className={`p-4 rounded-lg border ${
                  wechatSendResult.success 
                    ? 'bg-green-50 border-green-200 text-green-800' 
                    : 'bg-red-50 border-red-200 text-red-800'
                }`}>
                  <div className="flex items-center space-x-2">
                    <i className={`w-5 h-5 flex items-center justify-center ${
                      wechatSendResult.success ? 'ri-check-line' : 'ri-error-warning-line'
                    }`}></i>
                    <span className="font-medium">{wechatSendResult.message}</span>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowWeChatModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleSendToWeChat}
                  disabled={isSendingWeChat || !wechatConfig.recipientOpenId.trim()}
                  className="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer whitespace-nowrap flex items-center space-x-2"
                >
                  {isSendingWeChat ? (
                    <>
                      <i className="ri-loader-4-line w-4 h-4 flex items-center justify-center animate-spin"></i>
                      <span>Sending...</span>
                    </>
                  ) : (
                    <>
                      <i className="ri-send-plane-line w-4 h-4 flex items-center justify-center"></i>
                      <span>Send to WeChat</span>
                    </>
                  )}
                </button>
              </div>

              {/* Help Text */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <i className="ri-information-line w-5 h-5 flex items-center justify-center text-blue-600 mt-0.5"></i>
                  <div className="text-sm text-blue-800">
                    <p className="font-medium mb-1">WeChat Integration Requirements:</p>
                    <ul className="list-disc list-inside space-y-1 text-blue-700">
                      <li>Valid WeChat OpenID is required</li>
                      <li>Template messages provide better formatting and delivery rates</li>
                      <li>Make sure your WeChat app has proper messaging permissions</li>
                      <li>Test with a known OpenID before sending to customers</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* WhatsApp Send Modal */}
      {showWhatsAppModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <i className="ri-whatsapp-line w-6 h-6 flex items-center justify-center text-green-600"></i>
                <h3 className="text-lg font-semibold text-gray-900">Send Invoice to WhatsApp</h3>
              </div>
              <button
                onClick={() => setShowWhatsAppModal(false)}
                className="text-gray-400 hover:text-gray-600 cursor-pointer"
              >
                <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
              </button>
            </div>

            <div className="space-y-6">
              {/* Send Type Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">Send Method</label>
                <div className="grid grid-cols-2 gap-4">
                  <label className="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                    <input
                      type="radio"
                      value="template"
                      checked={whatsappConfig.sendType === 'template'}
                      onChange={(e) => setWhatsappConfig(prev => ({ ...prev, sendType: e.target.value as any }))}
                      className="mt-1 w-4 h-4 text-green-600 border-gray-300 focus:ring-green-500"
                    />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 mb-1">Template Message</div>
                      <div className="text-sm text-gray-500">Send as a formatted template message with invoice details</div>
                      <div className="mt-2 text-xs text-green-600 bg-green-50 px-2 py-1 rounded font-medium">Recommended</div>
                    </div>
                  </label>

                  <label className="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                    <input
                      type="radio"
                      value="direct"
                      checked={whatsappConfig.sendType === 'direct'}
                      onChange={(e) => setWhatsappConfig(prev => ({ ...prev, sendType: e.target.value as any }))}
                      className="mt-1 w-4 h-4 text-green-600 border-gray-300 focus:ring-green-500"
                    />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 mb-1">Direct Message</div>
                      <div className="text-sm text-gray-500">Send as a text message with custom content</div>
                    </div>
                  </label>
                </div>
              </div>

              {/* Recipient Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">WhatsApp Phone Number *</label>
                  <input
                    type="tel"
                    required
                    value={whatsappConfig.phoneNumber}
                    onChange={(e) => setWhatsappConfig(prev => ({ ...prev, phoneNumber: e.target.value }))}
                    placeholder="+1234567890"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                  />
                  <p className="mt-1 text-xs text-gray-500">Include country code (e.g., +1234567890)</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Recipient Name</label>
                  <input
                    type="text"
                    value={whatsappConfig.recipientName}
                    onChange={(e) => setWhatsappConfig(prev => ({ ...prev, recipientName: e.target.value }))}
                    placeholder="Enter recipient name (optional)"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                  />
                  <p className="mt-1 text-xs text-gray-500">Display name for the recipient</p>
                </div>
              </div>

              {/* Custom Message */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Additional Message</label>
                <textarea
                  rows={3}
                  value={whatsappConfig.message}
                  onChange={(e) => setWhatsappConfig(prev => ({ ...prev, message: e.target.value }))}
                  placeholder="Add a custom message to include with the invoice (optional)"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm resize-none"
                  maxLength={500}
                />
                <div className="mt-1 text-xs text-gray-500 text-right">
                  {whatsappConfig.message.length}/500 characters
                </div>
              </div>

              {/* Invoice Preview */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Invoice Preview</label>
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-60 overflow-y-auto">
                  <pre className="text-sm text-gray-700 whitespace-pre-wrap font-mono">
                    {generateWhatsAppInvoiceTemplate()}
                  </pre>
                </div>
              </div>

              {/* Send Result */}
              {whatsappSendResult && (
                <div className={`p-4 rounded-lg border ${
                  whatsappSendResult.success 
                    ? 'bg-green-50 border-green-200 text-green-800' 
                    : 'bg-red-50 border-red-200 text-red-800'
                }`}>
                  <div className="flex items-center space-x-2">
                    <i className={`w-5 h-5 flex items-center justify-center ${
                      whatsappSendResult.success ? 'ri-check-line' : 'ri-error-warning-line'
                    }`}></i>
                    <span className="font-medium">{whatsappSendResult.message}</span>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowWhatsAppModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleSendToWhatsApp}
                  disabled={isSendingWhatsApp || !whatsappConfig.phoneNumber.trim()}
                  className="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer whitespace-nowrap flex items-center space-x-2"
                >
                  {isSendingWhatsApp ? (
                    <>
                      <i className="ri-loader-4-line w-4 h-4 flex items-center justify-center animate-spin"></i>
                      <span>Sending...</span>
                    </>
                  ) : (
                    <>
                      <i className="ri-send-plane-line w-4 h-4 flex items-center justify-center"></i>
                      <span>Send to WhatsApp</span>
                    </>
                  )}
                </button>
              </div>

              {/* Help Text */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <i className="ri-information-line w-5 h-5 flex items-center justify-center text-green-600 mt-0.5"></i>
                  <div className="text-sm text-green-800">
                    <p className="font-medium mb-1">WhatsApp Integration Requirements:</p>
                    <ul className="list-disc list-inside space-y-1 text-green-700">
                      <li>Valid phone number with country code is required</li>
                      <li>Template messages provide better formatting and delivery rates</li>
                      <li>Make sure your WhatsApp Business API has proper permissions</li>
                      <li>Test with a known phone number before sending to customers</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

    </div>
  );
}
