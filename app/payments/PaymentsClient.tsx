
'use client';

import { useSalesStore } from '@/lib/salesStore';
import Link from 'next/link';
import { useState } from 'react';

export default function PaymentsClient() {
  const payments = useSalesStore(state => state.payments);
  const advancePayments = useSalesStore(state => state.advancePayments);
  const recordPayment = useSalesStore(state => state.recordPayment);
  
  const [statusFilter, setStatusFilter] = useState('All');
  const [typeFilter, setTypeFilter] = useState('All');
  const [sortBy, setSortBy] = useState('dueDate');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<string>('');

  // Combine regular payments and advance payments for unified view
  const allPayments = [
    ...payments.map(p => ({ ...p, type: 'Invoice' as const })),
    ...advancePayments.map(a => ({
      id: a.id,
      orderId: a.referenceNumber,
      customerName: a.customerName,
      amount: a.amount,
      paidAmount: a.amount,
      remainingAmount: 0,
      paymentMethod: a.paymentMethod,
      status: a.status === 'Active' ? 'Advance Payment' : 'Applied' as any,
      dueDate: a.date,
      paidDate: a.date,
      invoiceNumber: a.referenceNumber,
      notes: a.notes,
      type: 'Advance' as const
    }))
  ];

  const filteredPayments = allPayments
    .filter(payment => statusFilter === 'All' || payment.status === statusFilter)
    .filter(payment => typeFilter === 'All' || payment.type === typeFilter)
    .sort((a, b) => {
      if (sortBy === 'dueDate') return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
      if (sortBy === 'amount') return b.amount - a.amount;
      if (sortBy === 'outstanding') return b.remainingAmount - a.remainingAmount;
      return 0;
    });

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'Paid': return 'bg-green-100 text-green-800 border-green-200';
      case 'Partial': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Pending': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Overdue': return 'bg-red-100 text-red-800 border-red-200';
      case 'Advance Payment': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'Applied': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleRecordPayment = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const amount = parseFloat(formData.get('amount') as string);
    const method = formData.get('method') as any;
    const notes = formData.get('notes') as string;

    recordPayment(selectedPayment, amount, method, notes);
    setShowPaymentModal(false);
    setSelectedPayment('');
  };

  const totalOutstanding = payments.reduce((sum, payment) => sum + payment.remainingAmount, 0);
  const totalPaid = payments.reduce((sum, payment) => sum + payment.paidAmount, 0);
  const totalAdvancePayments = advancePayments.filter(a => a.status === 'Active').reduce((sum, a) => sum + a.amount, 0);
  const overdueCount = payments.filter(p => p.status === 'Overdue').length;
  const pendingCount = payments.filter(p => p.status === 'Pending').length;

  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Payment Tracking</h1>
        <p className="text-gray-600">Monitor customer payments, outstanding balances, and advance payments</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600">Total Outstanding</p>
              <p className="text-2xl font-bold text-red-600">${totalOutstanding.toLocaleString()}</p>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <i className="ri-alert-line w-6 h-6 flex items-center justify-center text-red-600"></i>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600">Total Paid</p>
              <p className="text-2xl font-bold text-green-600">${totalPaid.toLocaleString()}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <i className="ri-money-dollar-circle-line w-6 h-6 flex items-center justify-center text-green-600"></i>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600">Advance Payments</p>
              <p className="text-2xl font-bold text-purple-600">${totalAdvancePayments.toLocaleString()}</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <i className="ri-wallet-3-line w-6 h-6 flex items-center justify-center text-purple-600"></i>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600">Overdue Payments</p>
              <p className="text-2xl font-bold text-red-600">{overdueCount}</p>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <i className="ri-time-line w-6 h-6 flex items-center justify-center text-red-600"></i>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600">Pending Payments</p>
              <p className="text-2xl font-bold text-blue-600">{pendingCount}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <i className="ri-timer-line w-6 h-6 flex items-center justify-center text-blue-600"></i>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">All Payments & Advances</h3>
            <div className="text-sm text-gray-500">{filteredPayments.length} of {allPayments.length} payments</div>
          </div>

          <div className="flex items-center space-x-4 flex-wrap gap-2">
            <select 
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-8"
            >
              <option value="All">All Status</option>
              <option value="Paid">Paid</option>
              <option value="Partial">Partial</option>
              <option value="Pending">Pending</option>
              <option value="Overdue">Overdue</option>
              <option value="Advance Payment">Advance Payment</option>
              <option value="Applied">Applied</option>
            </select>

            <select 
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-8"
            >
              <option value="All">All Types</option>
              <option value="Invoice">Invoices</option>
              <option value="Advance">Advance Payments</option>
            </select>

            <div className="text-sm text-gray-500">Sort by:</div>
            
            <button
              onClick={() => setSortBy('dueDate')}
              className={`flex items-center space-x-1 px-3 py-2 text-sm font-medium rounded-lg cursor-pointer ${
                sortBy === 'dueDate' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <span>Date</span>
              {sortBy === 'dueDate' && <i className="w-4 h-4 flex items-center justify-center ri-arrow-down-line"></i>}
            </button>

            <button
              onClick={() => setSortBy('amount')}
              className={`flex items-center space-x-1 px-3 py-2 text-sm font-medium rounded-lg cursor-pointer ${
                sortBy === 'amount' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <span>Amount</span>
              {sortBy === 'amount' && <i className="w-4 h-4 flex items-center justify-center ri-arrow-down-line"></i>}
            </button>

            <button
              onClick={() => setSortBy('outstanding')}
              className={`flex items-center space-x-1 px-3 py-2 text-sm font-medium rounded-lg cursor-pointer ${
                sortBy === 'outstanding' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <span>Outstanding</span>
              {sortBy === 'outstanding' && <i className="w-4 h-4 flex items-center justify-center ri-arrow-down-line"></i>}
            </button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Type</th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Reference</th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Customer</th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Order ID</th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Total Amount</th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Paid Amount</th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Outstanding</th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Method</th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredPayments.map((payment) => (
                <tr key={payment.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      payment.type === 'Invoice' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
                    }`}>
                      {payment.type}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm font-semibold text-blue-600">{payment.invoiceNumber}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Link href={`/customers/${encodeURIComponent(payment.customerName)}`}>
                      <span className="text-sm text-blue-600 hover:text-blue-800 cursor-pointer">{payment.customerName}</span>
                    </Link>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {payment.type === 'Invoice' ? (
                      <Link href={`/sales/${payment.orderId}`}>
                        <span className="text-sm text-blue-600 hover:text-blue-800 cursor-pointer">{payment.orderId}</span>
                      </Link>
                    ) : (
                      <span className="text-sm text-gray-500">-</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-semibold text-gray-900">${payment.amount.toLocaleString()}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-semibold text-green-600">${payment.paidAmount.toLocaleString()}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`text-sm font-semibold ${payment.remainingAmount > 0 ? 'text-red-600' : 'text-green-600'}`}>
                      ${payment.remainingAmount.toLocaleString()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <i className="ri-calendar-line w-4 h-4 flex items-center justify-center text-gray-400"></i>
                      <span className={`text-sm ${
                        payment.status === 'Overdue' ? 'text-red-600 font-medium' : 'text-gray-700'
                      }`}>
                        {new Date(payment.dueDate).toLocaleDateString()}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <i className={`w-4 h-4 flex items-center justify-center text-gray-400 ${
                        payment.paymentMethod === 'Cash' ? 'ri-money-dollar-circle-line' :
                        payment.paymentMethod === 'Credit Card' ? 'ri-bank-card-line' :
                        payment.paymentMethod === 'Bank Transfer' ? 'ri-bank-line' :
                        payment.paymentMethod === 'Check' ? 'ri-file-text-line' : 'ri-paypal-line'
                      }`}></i>
                      <span className="text-sm text-gray-700">{payment.paymentMethod}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getPaymentStatusColor(payment.status)}`}>
                      <div className={`w-2 h-2 rounded-full mr-2 ${
                        payment.status === 'Paid' ? 'bg-green-500' :
                        payment.status === 'Partial' ? 'bg-yellow-500' :
                        payment.status === 'Pending' ? 'bg-blue-500' : 
                        payment.status === 'Advance Payment' ? 'bg-purple-500' :
                        payment.status === 'Applied' ? 'bg-gray-500' : 'bg-red-500'
                      }`}></div>
                      {payment.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    {payment.remainingAmount > 0 && payment.type === 'Invoice' && (
                      <button
                        onClick={() => {
                          setSelectedPayment(payment.id);
                          setShowPaymentModal(true);
                        }}
                        className="text-blue-600 hover:text-blue-800 font-medium text-sm cursor-pointer whitespace-nowrap"
                      >
                        Record Payment
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Record Payment</h3>
              <button
                onClick={() => setShowPaymentModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
              </button>
            </div>

            <form onSubmit={handleRecordPayment} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Payment Amount</label>
                <input
                  type="number"
                  name="amount"
                  step="0.01"
                  min="0.01"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0.00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                <select
                  name="method"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 pr-8"
                >
                  <option value="">Select method</option>
                  <option value="Cash">Cash</option>
                  <option value="Credit Card">Credit Card</option>
                  <option value="Bank Transfer">Bank Transfer</option>
                  <option value="Check">Check</option>
                  <option value="PayPal">PayPal</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Notes (Optional)</label>
                <textarea
                  name="notes"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Payment notes..."
                ></textarea>
              </div>

              <div className="flex items-center justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowPaymentModal(false)}
                  className="px-4 py-2 text-gray-700 hover:text-gray-900 cursor-pointer whitespace-nowrap"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap"
                >
                  Record Payment
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
