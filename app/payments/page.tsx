
'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import PaymentsClient from './PaymentsClient';

export default function PaymentsPage() {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [navMode, setNavMode] = useState<'sidebar' | 'topnav'>('sidebar');

  // Load saved navigation mode from localStorage on component mount
  useEffect(() => {
    const savedNavMode = localStorage.getItem('navMode') as 'sidebar' | 'topnav' | null;
    if (savedNavMode) {
      setNavMode(savedNavMode);
    }
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
      <main className={`transition-all duration-300 p-6 ${
        navMode === 'topnav' ? '' : (sidebarOpen ? 'pl-64' : 'pl-16')
      }`}>
        <PaymentsClient />
      </main>
    </div>
  );
}
