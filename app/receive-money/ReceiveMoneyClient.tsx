
'use client';

import { useState, useEffect } from 'react';
import { useSalesStore } from '@/lib/salesStore';
import SalesStore from '@/lib/salesStore';
import Link from 'next/link';

export default function ReceiveMoneyClient() {
  const payments = useSalesStore(state => state.payments);
  const recordPayment = useSalesStore(state => state.recordPayment);
  
  const [selectedPayments, setSelectedPayments] = useState<string[]>([]);
  const [showReceiveModal, setShowReceiveModal] = useState(false);
  const [statusFilter, setStatusFilter] = useState('Outstanding');
  const [sortBy, setSortBy] = useState('dueDate');
  const [searchTerm, setSearchTerm] = useState('');

  const filteredPayments = payments
    .filter(payment => {
      const matchesStatus = statusFilter === 'All' || 
        (statusFilter === 'Outstanding' && payment.remainingAmount > 0) ||
        payment.status === statusFilter;
      const matchesSearch = searchTerm === '' || 
        payment.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        payment.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        payment.orderId.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesStatus && matchesSearch;
    })
    .sort((a, b) => {
      if (sortBy === 'dueDate') return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
      if (sortBy === 'amount') return b.remainingAmount - a.remainingAmount;
      if (sortBy === 'customer') return a.customerName.localeCompare(b.customerName);
      return 0;
    });

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'Paid': return 'bg-green-100 text-green-800 border-green-200';
      case 'Partial': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Pending': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Overdue': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const outstandingPayments = filteredPayments
        .filter(p => p.remainingAmount > 0)
        .map(p => p.id);
      setSelectedPayments(outstandingPayments);
    } else {
      setSelectedPayments([]);
    }
  };

  const handleSelectPayment = (paymentId: string, checked: boolean) => {
    if (checked) {
      setSelectedPayments(prev => [...prev, paymentId]);
    } else {
      setSelectedPayments(prev => prev.filter(id => id !== paymentId));
    }
  };

  const handleBulkReceive = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const method = formData.get('bulkMethod') as any;
    const notes = formData.get('bulkNotes') as string;

    selectedPayments.forEach(paymentId => {
      const payment = payments.find(p => p.id === paymentId);
      if (payment && payment.remainingAmount > 0) {
        recordPayment(paymentId, payment.remainingAmount, method, notes);
      }
    });

    setSelectedPayments([]);
    setShowReceiveModal(false);
  };

  const handleSingleReceive = (paymentId: string, amount: number, method: any, notes?: string) => {
    recordPayment(paymentId, amount, method, notes);
  };

  const totalOutstanding = filteredPayments.reduce((sum, payment) => sum + payment.remainingAmount, 0);
  const selectedAmount = selectedPayments.reduce((sum, paymentId) => {
    const payment = payments.find(p => p.id === paymentId);
    return sum + (payment?.remainingAmount || 0);
  }, 0);

  const overduePayments = filteredPayments.filter(p => p.status === 'Overdue');
  const dueSoonPayments = filteredPayments.filter(p => {
    const dueDate = new Date(p.dueDate);
    const today = new Date();
    const daysDiff = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    return daysDiff <= 7 && daysDiff >= 0 && p.remainingAmount > 0;
  });

  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Receive Money</h1>
        <p className="text-gray-600">Record customer payments and manage cash flow</p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600">Total Outstanding</p>
              <p className="text-2xl font-bold text-red-600">${totalOutstanding.toLocaleString()}</p>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <i className="ri-money-dollar-circle-line w-6 h-6 flex items-center justify-center text-red-600"></i>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600">Overdue</p>
              <p className="text-2xl font-bold text-red-600">{overduePayments.length}</p>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <i className="ri-alert-line w-6 h-6 flex items-center justify-center text-red-600"></i>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600">Due This Week</p>
              <p className="text-2xl font-bold text-yellow-600">{dueSoonPayments.length}</p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <i className="ri-time-line w-6 h-6 flex items-center justify-center text-yellow-600"></i>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600">Selected Amount</p>
              <p className="text-2xl font-bold text-blue-600">${selectedAmount.toLocaleString()}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <i className="ri-checkbox-line w-6 h-6 flex items-center justify-center text-blue-600"></i>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Outstanding Invoices</h3>
            <div className="flex items-center space-x-3">
              {selectedPayments.length > 0 && (
                <button
                  onClick={() => setShowReceiveModal(true)}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 cursor-pointer whitespace-nowrap flex items-center space-x-2"
                >
                  <i className="ri-money-dollar-circle-line w-4 h-4 flex items-center justify-center"></i>
                  <span>Receive Selected ({selectedPayments.length})</span>
                </button>
              )}
              <Link href="/payments">
                <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap">
                  View All Payments
                </button>
              </Link>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search by customer, invoice, or order ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              />
            </div>
            
            <select 
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-8"
            >
              <option value="Outstanding">Outstanding Only</option>
              <option value="All">All Status</option>
              <option value="Pending">Pending</option>
              <option value="Partial">Partial</option>
              <option value="Overdue">Overdue</option>
              <option value="Paid">Paid</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-8"
            >
              <option value="dueDate">Sort by Due Date</option>
              <option value="amount">Sort by Amount</option>
              <option value="customer">Sort by Customer</option>
            </select>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-4 text-left">
                  <input
                    type="checkbox"
                    checked={selectedPayments.length > 0 && selectedPayments.length === filteredPayments.filter(p => p.remainingAmount > 0).length}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Invoice</th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Customer</th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Order ID</th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Total Amount</th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Outstanding</th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Due Date</th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredPayments.map((payment) => (
                <tr key={payment.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    {payment.remainingAmount > 0 && (
                      <input
                        type="checkbox"
                        checked={selectedPayments.includes(payment.id)}
                        onChange={(e) => handleSelectPayment(payment.id, e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm font-semibold text-blue-600">{payment.invoiceNumber}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Link href={`/customers/${encodeURIComponent(payment.customerName)}`}>
                      <span className="text-sm text-blue-600 hover:text-blue-800 cursor-pointer">{payment.customerName}</span>
                    </Link>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Link href={`/sales/${payment.orderId}`}>
                      <span className="text-sm text-blue-600 hover:text-blue-800 cursor-pointer">{payment.orderId}</span>
                    </Link>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-semibold text-gray-900">${payment.amount.toLocaleString()}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`text-sm font-semibold ${payment.remainingAmount > 0 ? 'text-red-600' : 'text-green-600'}`}>
                      ${payment.remainingAmount.toLocaleString()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <i className="ri-calendar-line w-4 h-4 flex items-center justify-center text-gray-400"></i>
                      <span className={`text-sm ${
                        payment.status === 'Overdue' ? 'text-red-600 font-medium' : 'text-gray-700'
                      }`}>
                        {new Date(payment.dueDate).toLocaleDateString()}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getPaymentStatusColor(payment.status)}`}>
                      <div className={`w-2 h-2 rounded-full mr-2 ${
                        payment.status === 'Paid' ? 'bg-green-500' :
                        payment.status === 'Partial' ? 'bg-yellow-500' :
                        payment.status === 'Pending' ? 'bg-blue-500' : 'bg-red-500'
                      }`}></div>
                      {payment.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    {payment.remainingAmount > 0 && (
                      <ReceivePaymentButton 
                        payment={payment} 
                        onReceive={handleSingleReceive}
                      />
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Bulk Receive Modal */}
      {showReceiveModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Receive Selected Payments</h3>
              <button
                onClick={() => setShowReceiveModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
              </button>
            </div>

            <div className="mb-4">
              <p className="text-sm text-gray-600">
                Receiving payment for {selectedPayments.length} invoice(s)
              </p>
              <p className="text-lg font-semibold text-green-600">
                Total: ${selectedAmount.toLocaleString()}
              </p>
            </div>

            <form onSubmit={handleBulkReceive} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                <select
                  name="bulkMethod"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 pr-8"
                >
                  <option value="">Select method</option>
                  <option value="Cash">Cash</option>
                  <option value="Credit Card">Credit Card</option>
                  <option value="Bank Transfer">Bank Transfer</option>
                  <option value="Check">Check</option>
                  <option value="PayPal">PayPal</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Notes (Optional)</label>
                <textarea
                  name="bulkNotes"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Payment notes..."
                ></textarea>
              </div>

              <div className="flex items-center justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowReceiveModal(false)}
                  className="px-4 py-2 text-gray-700 hover:text-gray-900 cursor-pointer whitespace-nowrap"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 cursor-pointer whitespace-nowrap"
                >
                  Receive Payments
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}

function ReceivePaymentButton({ payment, onReceive }: { 
  payment: any; 
  onReceive: (paymentId: string, amount: number, method: any, notes?: string) => void;
}) {
  const [showModal, setShowModal] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const amount = parseFloat(formData.get('amount') as string);
    const method = formData.get('method') as any;
    const notes = formData.get('notes') as string;

    onReceive(payment.id, amount, method, notes);
    setShowModal(false);
  };

  return (
    <>
      <button
        onClick={() => setShowModal(true)}
        className="text-green-600 hover:text-green-800 font-medium text-sm cursor-pointer whitespace-nowrap"
      >
        Receive
      </button>

      {showModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Receive Payment</h3>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
              </button>
            </div>

            <div className="mb-4">
              <p className="text-sm text-gray-600">Invoice: {payment.invoiceNumber}</p>
              <p className="text-sm text-gray-600">Customer: {payment.customerName}</p>
              <p className="text-sm text-gray-600">Outstanding: ${payment.remainingAmount.toLocaleString()}</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Payment Amount</label>
                <input
                  type="number"
                  name="amount"
                  step="0.01"
                  min="0.01"
                  max={payment.remainingAmount}
                  defaultValue={payment.remainingAmount}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                <select
                  name="method"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 pr-8"
                >
                  <option value="">Select method</option>
                  <option value="Cash">Cash</option>
                  <option value="Credit Card">Credit Card</option>
                  <option value="Bank Transfer">Bank Transfer</option>
                  <option value="Check">Check</option>
                  <option value="PayPal">PayPal</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Notes (Optional)</label>
                <textarea
                  name="notes"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Payment notes..."
                ></textarea>
              </div>

              <div className="flex items-center justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 text-gray-700 hover:text-gray-900 cursor-pointer whitespace-nowrap"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 cursor-pointer whitespace-nowrap"
                >
                  Receive Payment
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  );
}
