
  const processPasteData = (data: string) => {
    if (!data.trim()) {
      setPastePreview([]);
      return;
    }

    // Handle different paste formats
    let rows: string[][] = [];
    
    // Check if data contains tabs (Excel table format)
    if (data.includes('\t')) {
      // Split by lines and then by tabs (standard Excel copy-paste)
      const lines = data.trim().split('\n');
      rows = lines.map(line => line.split('\t'));
    } else if (data.includes(',')) {
      // Handle CSV format
      const lines = data.trim().split('\n');
      rows = lines.map(line => {
        // Simple CSV parsing (handles basic cases)
        return line.split(',').map(cell => cell.trim().replace(/^["']|["']$/g, ''));
      });
    } else {
      // Handle single column or space-separated data
      const lines = data.trim().split('\n');
      if (lines.length > 1) {
        // Multiple lines - treat each line as a row with single column
        rows = lines.map(line => [line.trim()]);
      } else {
        // Single line - split by spaces or treat as single cell
        const items = data.trim().split(/\s+/);
        if (items.length > 1) {
          // Multiple items - each item becomes a row
          rows = items.map(item => [item.trim()]);
        } else {
          // Single item
          rows = [[data.trim()]];
        }
      }
    }
    
    // Remove empty rows
    const filteredRows = rows.filter(row => row.some(cell => cell && cell.trim() !== ''));
    
    setPastePreview(filteredRows);
    
    // Auto-map columns based on common patterns
    if (filteredRows.length > 0) {
      const headerRow = filteredRows[0];
      const autoMapping: Record<number, string> = {};
      
      headerRow.forEach((header, index) => {
        const lowerHeader = header.toLowerCase().trim();
        
        // Auto-detect common patterns
        if (lowerHeader.includes('product') || lowerHeader.includes('name') || lowerHeader.includes('item')) {
          autoMapping[index] = 'name';
        } else if (lowerHeader.includes('supplier') || lowerHeader.includes('vendor')) {
          autoMapping[index] = 'supplier';
        } else if (lowerHeader.includes('supplier price') || lowerHeader.includes('cost')) {
          autoMapping[index] = 'supplierPrice';
        } else if (lowerHeader.includes('sales price') || lowerHeader.includes('price') || lowerHeader.includes('sell')) {
          autoMapping[index] = 'salesPrice';
        } else if (lowerHeader.includes('quantity') || lowerHeader.includes('qty')) {
          autoMapping[index] = 'quantity';
        } else if (lowerHeader.includes('sku')) {
          // Map to first available custom column or create one
          const skuColumn = customColumns.find(col => col.name.toLowerCase().includes('sku'));
          if (skuColumn) {
            autoMapping[index] = skuColumn.id;
          }
        } else if (filteredRows.length > 1) {
          // If we have multiple rows and this looks like product data, assume first column is product name
          if (index === 0) {
            autoMapping[index] = 'name';
          }
        }
      });
      
      // If no auto-mapping detected and we have data, suggest mapping first column to product name
      if (Object.keys(autoMapping).length === 0 && filteredRows.length > 0) {
        autoMapping[0] = 'name';
      }
      
      setColumnMapping(autoMapping);
    }
  };

  const applyPasteData = () => {
    if (pastePreview.length === 0) return;
    
    // Determine if first row is header based on column mapping
    const hasHeader = Object.keys(columnMapping).length > 0 && 
      pastePreview.length > 1 && 
      pastePreview[0].some(cell => 
        cell.toLowerCase().includes('product') || 
        cell.toLowerCase().includes('name') || 
        cell.toLowerCase().includes('price') ||
        cell.toLowerCase().includes('supplier')
      );
    
    const dataRows = hasHeader ? pastePreview.slice(1) : pastePreview;
    
    if (dataRows.length === 0) return;
    
    // Create new product rows from paste data
    const newRows = dataRows.map((row, index) => {
      const newRow = {
        id: `row-${Date.now()}-${index}`,
        name: '',
        supplier: '',
        supplierPrice: '',
        salesPrice: '',
        quantity: '1',
        quantityUnit: globalQuantityUnit,
        photo: null as File | null,
        photoPreview: '',
        customData: {} as Record<string, string>
      };
      
      // Initialize custom data for all existing custom columns
      customColumns.forEach(col => {
        newRow.customData[col.id] = '';
      });
      
      // Map data based on column mapping
      Object.entries(columnMapping).forEach(([colIndex, fieldName]) => {
        const cellValue = row[parseInt(colIndex)] || '';
        const trimmedValue = cellValue.trim();
        
        if (fieldName === 'name') {
          newRow.name = trimmedValue;
        } else if (fieldName === 'supplier') {
          newRow.supplier = trimmedValue;
        } else if (fieldName === 'supplierPrice') {
          // Clean price data - remove currency symbols and keep only numbers and decimal point
          const cleanPrice = trimmedValue.replace(/[^0-9.]/g, '');
          newRow.supplierPrice = cleanPrice;
        } else if (fieldName === 'salesPrice') {
          // Clean price data - remove currency symbols and keep only numbers and decimal point
          const cleanPrice = trimmedValue.replace(/[^0-9.]/g, '');
          newRow.salesPrice = cleanPrice;
        } else if (fieldName === 'quantity') {
          // Clean quantity data - keep only numbers and decimal point
          const cleanQty = trimmedValue.replace(/[^0-9.]/g, '');
          newRow.quantity = cleanQty || '1';
        } else if (fieldName.startsWith('custom-')) {
          // Handle custom column data
          newRow.customData[fieldName] = trimmedValue;
        }
      });
      
      // If no mapping was set, but we have data, try to intelligently fill
      if (Object.keys(columnMapping).length === 0 && row.length > 0) {
        // Use first column as product name
        newRow.name = row[0].trim();
        
        // If we have more columns, try to use them intelligently
        if (row.length > 1) {
          // Second column could be supplier
          newRow.supplier = row[1].trim();
        }
        if (row.length > 2) {
          // Third column could be price
          const priceValue = row[2].replace(/[^0-9.]/g, '');
          if (priceValue) {
            newRow.salesPrice = priceValue;
          }
        }
      }
      
      return newRow;
    });
    
    // Ask user whether to replace or append
    const replace = confirm(
      `Found ${newRows.length} products to import.\n\n` +
      `Click OK to REPLACE existing products\n` +
      `Click Cancel to ADD to existing products`
    );
    
    if (replace) {
      setProductRows(newRows);
    } else {
      setProductRows(prev => [...prev, ...newRows]);
    }
    
    setShowPasteModal(false);
    setPasteData('');
    setPastePreview([]);
    setColumnMapping({});
    
    // Show success message
    alert(`Successfully imported ${newRows.length} products!`);
  };
