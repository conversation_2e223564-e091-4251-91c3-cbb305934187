
'use client';

import { useState, useEffect } from 'react';
import Header from '../../components/Header';
import ProductTable from '../../components/ProductTable';

export default function ProductsPage() {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [navMode, setNavMode] = useState<'sidebar' | 'topnav'>('sidebar');

  // Load saved navigation mode from localStorage on component mount
  useEffect(() => {
    const savedNavMode = localStorage.getItem('navMode') as 'sidebar' | 'topnav' | null;
    if (savedNavMode) {
      setNavMode(savedNavMode);
    }
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
      
      <div className={`transition-all duration-300 ${
        navMode === 'topnav' ? '' : (sidebarOpen ? 'pl-64' : 'pl-0')
      }`}>
        <div className="pt-16 p-6">
          <ProductTable />
        </div>
      </div>
    </div>
  );
}
