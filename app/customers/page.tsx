
'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import SalesStore, { Sale } from '@/lib/salesStore';
import { useTranslation } from '@/hooks/useTranslation';

interface CustomerData {
  name: string;
  totalOrders: number;
  totalAmount: number;
  totalCommission: number;
  lastOrderDate: string;
  status: 'Active' | 'Inactive';
  orders: Sale[];
}

export default function CustomersPage() {
  const { t } = useTranslation();

  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [navMode, setNavMode] = useState<'sidebar' | 'topnav'>('sidebar');
  const [customers, setCustomers] = useState<CustomerData[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'All' | 'Active' | 'Inactive'>('All');
  const [sortBy, setSortBy] = useState<'name' | 'orders' | 'amount' | 'date'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [showAddCustomerModal, setShowAddCustomerModal] = useState(false);
  const [newCustomer, setNewCustomer] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    address: ''
  });

  // Load saved navigation mode from localStorage on component mount
  useEffect(() => {
    const savedNavMode = localStorage.getItem('navMode') as 'sidebar' | 'topnav' | null;
    if (savedNavMode) {
      setNavMode(savedNavMode);
    }
  }, []);

  // Fixed customer data loading function
  const loadCustomerData = () => {
    try {
      const salesStore = SalesStore.getInstance();
      const sales = salesStore.getSales();

      // Group sales by customer
      const customerMap = new Map<string, Sale[]>();

      sales.forEach(sale => {
        const customerName = sale.customer;
        if (!customerMap.has(customerName)) {
          customerMap.set(customerName, []);
        }
        customerMap.get(customerName)!.push(sale);
      });

      // Convert to customer data from sales
      const salesCustomerData: CustomerData[] = Array.from(customerMap.entries()).map(([name, orders]) => {
        const totalAmount = orders.reduce((sum, order) =>
          sum + parseFloat(order.total.replace(/[$,]/g, '')), 0
        );
        const totalCommission = orders.reduce((sum, order) =>
          sum + parseFloat(order.commission.replace(/[$,]/g, '')), 0
        );

        // Get the most recent order date
        const lastOrderDate = orders.reduce((latest, order) => {
          const orderDate = new Date(order.salesDate);
          return orderDate > new Date(latest) ? order.salesDate : latest;
        }, orders[0].salesDate);

        // Determine if customer is active (ordered within last 30 days)
        const daysSinceLastOrder = Math.floor(
          (new Date().getTime() - new Date(lastOrderDate).getTime()) / (1000 * 60 * 60 * 24)
        );
        const status: 'Active' | 'Inactive' = daysSinceLastOrder <= 30 ? 'Active' : 'Inactive';

        return {
          name,
          totalOrders: orders.length,
          totalAmount,
          totalCommission,
          lastOrderDate,
          status,
          orders: orders.sort((a, b) => new Date(b.salesDate).getTime() - new Date(a.salesDate).getTime())
        };
      });

      // Load customer profiles (customers added via modal without orders)
      const customerProfiles = JSON.parse(localStorage.getItem('customerProfiles') || '{}');
      const profileCustomerData: CustomerData[] = Object.values(customerProfiles)
        .filter((profile: any) => !salesCustomerData.some(sc => sc.name === profile.name))
        .map((profile: any) => ({
          name: profile.name,
          totalOrders: 0,
          totalAmount: 0,
          totalCommission: 0,
          lastOrderDate: profile.createdDate || new Date().toISOString().split('T')[0],
          status: profile.status || 'Inactive',
          orders: []
        }));

      // Combine both sources
      const allCustomers = [...salesCustomerData, ...profileCustomerData];
      setCustomers(allCustomers);
    } catch (error) {
      console.error('Error loading customer data:', error);
      setCustomers([]);
    }
  };

  useEffect(() => {
    loadCustomerData();

    // Subscribe to sales changes
    const salesStore = SalesStore.getInstance();
    const unsubscribe = salesStore.subscribe(() => {
      loadCustomerData();
    });

    return unsubscribe;
  }, []);

  const handleAddCustomer = () => {
    try {
      if (!newCustomer.name.trim()) {
        alert('Customer name is required');
        return;
      }

      // Check if customer already exists in both sales and profiles
      const existingInCustomers = customers.find(c =>
        c.name.toLowerCase() === newCustomer.name.trim().toLowerCase()
      );

      if (existingInCustomers) {
        alert('A customer with this name already exists');
        return;
      }

      // Save customer profile to localStorage for persistence
      const existingProfiles = JSON.parse(localStorage.getItem('customerProfiles') || '{}');
      const customerKey = newCustomer.name.trim();
      
      existingProfiles[customerKey] = {
        name: newCustomer.name.trim(),
        email: newCustomer.email.trim(),
        phone: newCustomer.phone.trim(),
        company: newCustomer.company.trim(),
        address: newCustomer.address.trim(),
        createdDate: new Date().toISOString().split('T')[0],
        status: 'Inactive'
      };
      
      localStorage.setItem('customerProfiles', JSON.stringify(existingProfiles));

      // Create new customer data for immediate UI update
      const newCustomerData: CustomerData = {
        name: newCustomer.name.trim(),
        totalOrders: 0,
        totalAmount: 0,
        totalCommission: 0,
        lastOrderDate: new Date().toISOString().split('T')[0],
        status: 'Inactive',
        orders: []
      };

      // Update state immediately
      setCustomers(prev => [newCustomerData, ...prev]);

      // Reset form and close modal
      setNewCustomer({ name: '', email: '', phone: '', company: '', address: '' });
      setShowAddCustomerModal(false);

      alert(`Customer "${newCustomerData.name}" has been added successfully!`);
    } catch (error) {
      console.error('Error adding customer:', error);
      alert('Error adding customer. Please try again.');
    }
  };

  // Filter and sort customers
  const filteredCustomers = customers
    .filter(customer => {
      const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'All' || customer.status === statusFilter;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name;
          bValue = b.name;
          break;
        case 'orders':
          aValue = a.totalOrders;
          bValue = b.totalOrders;
          break;
        case 'amount':
          aValue = a.totalAmount;
          bValue = b.totalAmount;
          break;
        case 'date':
          aValue = new Date(a.lastOrderDate);
          bValue = new Date(b.lastOrderDate);
          break;
        default:
          aValue = a.name;
          bValue = b.name;
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

  const getStatusColor = (status: string) => {
    return status === 'Active'
      ? 'bg-green-100 text-green-800 border-green-200'
      : 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const toggleSort = (column: 'name' | 'orders' | 'amount' | 'date') => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

      <main className={`transition-all duration-300 p-6 ${
        navMode === 'topnav' ? '' : (sidebarOpen ? 'pl-64' : 'pl-16')
      }`}>
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{t('customers.title')}</h1>
                <p className="text-gray-600">{t('customers.subtitle')}</p>
              </div>
              <button
                onClick={() => setShowAddCustomerModal(true)}
                className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap"
              >
                <i className="ri-user-add-line w-4 h-4 flex items-center justify-center"></i>
                <span>{t('customers.addCustomer')}</span>
              </button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600">{t('customers.totalCustomers')}</p>
                  <p className="text-2xl font-bold text-gray-900">{customers.length}</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <i className="ri-user-line w-6 h-6 flex items-center justify-center text-blue-600"></i>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600">Active Customers</p>
                  <p className="text-2xl font-bold text-green-600">
                    {customers.filter(c => c.status === 'Active').length}
                  </p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <i className="ri-user-heart-line w-6 h-6 flex items-center justify-center text-green-600"></i>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600">Total Orders</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {customers.reduce((sum, customer) => sum + customer.totalOrders, 0)}
                  </p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <i className="ri-shopping-cart-line w-6 h-6 flex items-center justify-center text-purple-600"></i>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                  <p className="text-2xl font-bold text-orange-600">
                    ${customers.reduce((sum, customer) => sum + customer.totalAmount, 0).toLocaleString()}
                  </p>
                </div>
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <i className="ri-money-dollar-circle-line w-6 h-6 flex items-center justify-center text-orange-600"></i>
                </div>
              </div>
            </div>
          </div>

          {/* Filters and Search */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <i className="ri-search-line w-5 h-5 flex items-center justify-center absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                  <input
                    type="text"
                    placeholder={t('common.search') + '...'}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-64"
                  />
                </div>

                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as any)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-8"
                >
                  <option value="All">{t('common.status')} - {t('common.all')}</option>
                  <option value="Active">{t('customers.active')}</option>
                  <option value="Inactive">{t('customers.inactive')}</option>
                </select>
              </div>

              <div className="text-sm text-gray-500">
                {t('common.showing')} {filteredCustomers.length} {t('common.of')} {customers.length} {t('customers.title').toLowerCase()}
              </div>
            </div>
          </div>

          {/* Customers Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => toggleSort('name')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>{t('customers.tableHeaders.name') || 'Customer Name'}</span>
                        {sortBy === 'name' && (
                          <i className={`w-4 h-4 flex items-center justify-center ${sortOrder === 'asc' ? 'ri-arrow-up-line' : 'ri-arrow-down-line'}`}></i>
                        )}
                      </div>
                    </th>
                    <th
                      className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => toggleSort('orders')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>{t('customers.tableHeaders.totalOrders') || 'Total Orders'}</span>
                        {sortBy === 'orders' && (
                          <i className={`w-4 h-4 flex items-center justify-center ${sortOrder === 'asc' ? 'ri-arrow-up-line' : 'ri-arrow-down-line'}`}></i>
                        )}
                      </div>
                    </th>
                    <th
                      className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => toggleSort('amount')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>{t('customers.tableHeaders.totalAmount') || 'Total Amount'}</span>
                        {sortBy === 'amount' && (
                          <i className={`w-4 h-4 flex items-center justify-center ${sortOrder === 'asc' ? 'ri-arrow-up-line' : 'ri-arrow-down-line'}`}></i>
                        )}
                      </div>
                    </th>
                    <th
                      className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => toggleSort('date')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>{t('customers.tableHeaders.lastOrder') || 'Last Order'}</span>
                        {sortBy === 'date' && (
                          <i className={`w-4 h-4 flex items-center justify-center ${sortOrder === 'asc' ? 'ri-arrow-up-line' : 'ri-arrow-down-line'}`}></i>
                        )}
                      </div>
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      {t('customers.tableHeaders.status') || 'Status'}
                    </th>
                    <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      {t('customers.tableHeaders.actions') || 'Actions'}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredCustomers.map((customer) => (
                    <tr key={customer.name} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                              <span className="text-sm font-semibold text-blue-600">
                                {customer.name.charAt(0).toUpperCase()}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <Link href={`/customers/${encodeURIComponent(customer.name)}`}>
                              <div className="text-sm font-semibold text-blue-600 hover:text-blue-800 cursor-pointer">
                                {customer.name}
                              </div>
                            </Link>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-semibold text-gray-900">
                          {customer.totalOrders}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-semibold text-green-600">
                          ${customer.totalAmount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          <i className="ri-calendar-line w-4 h-4 flex items-center justify-center text-gray-400"></i>
                          <span className="text-sm text-gray-700">
                            {new Date(customer.lastOrderDate).toLocaleDateString()}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(customer.status)}`}>
                          <div className={`w-2 h-2 rounded-full mr-2 ${customer.status === 'Active' ? 'bg-green-500' : 'bg-gray-500'}`}></div>
                          {customer.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <Link href={`/customers/${encodeURIComponent(customer.name)}`}>
                          <button className="text-blue-600 hover:text-blue-800 font-medium text-sm cursor-pointer whitespace-nowrap">
                            {t('customers.tableHeaders.viewDetails') || 'View Details'}
                          </button>
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredCustomers.length === 0 && (
              <div className="text-center py-16">
                <i className="ri-user-search-line w-16 h-16 flex items-center justify-center mx-auto text-gray-300 mb-4"></i>
                <h3 className="text-lg font-medium text-gray-900 mb-2">{t('customers.noResults') || 'No customers found'}</h3>
                <p className="text-sm text-gray-500">{t('customers.noResultsMessage') || 'Try adjusting your search or filters to see more results.'}</p>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Add Customer Modal */}
      {showAddCustomerModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-lg mx-4">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">{t('customers.addCustomerModal.title')}</h3>
              <button
                onClick={() => setShowAddCustomerModal(false)}
                className="text-gray-400 hover:text-gray-600 cursor-pointer"
              >
                <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('customers.customerName')} *
                </label>
                <input
                  type="text"
                  required
                  placeholder={t('customers.customerName')}
                  value={newCustomer.name}
                  onChange={(e) => setNewCustomer(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('common.email')}
                </label>
                <input
                  type="email"
                  placeholder={t('common.email')}
                  value={newCustomer.email}
                  onChange={(e) => setNewCustomer(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('common.phone')}
                </label>
                <input
                  type="tel"
                  placeholder={t('common.phone')}
                  value={newCustomer.phone}
                  onChange={(e) => setNewCustomer(prev => ({ ...prev, phone: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('customers.addCustomerModal.company')}
                </label>
                <input
                  type="text"
                  placeholder={t('customers.addCustomerModal.company')}
                  value={newCustomer.company}
                  onChange={(e) => setNewCustomer(prev => ({ ...prev, company: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('common.address')}
                </label>
                <textarea
                  placeholder={t('common.address')}
                  value={newCustomer.address}
                  onChange={(e) => setNewCustomer(prev => ({ ...prev, address: e.target.value }))}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm resize-none"
                />
              </div>

              <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                <div className="flex items-start space-x-3">
                  <i className="ri-information-line w-5 h-5 flex items-center justify-center text-blue-600 mt-0.5"></i>
                  <div>
                    <h4 className="text-sm font-medium text-blue-900 mb-1">{t('customers.customerInfo')}</h4>
                    <p className="text-sm text-blue-700">
                      {t('customers.addCustomerModal.infoNote')}
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowAddCustomerModal(false)}
                  className="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap"
                >
                  {t('common.cancel')}
                </button>
                <button
                  type="button"
                  onClick={handleAddCustomer}
                  disabled={!newCustomer.name.trim()}
                  className="px-6 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer whitespace-nowrap"
                >
                  {t('customers.addCustomer')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
