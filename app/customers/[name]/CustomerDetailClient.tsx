
'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import Header from '@/components/Header';
import SalesStore, { Sale, useSalesStore } from '@/lib/salesStore';
import { useTranslation } from '@/hooks/useTranslation';

interface CustomerStats {
  totalOrders: number;
  totalAmount: number;
  totalCommission: number;
  averageOrderValue: number;
  firstOrderDate: string;
  lastOrderDate: string;
  status: 'Active' | 'Inactive';
}

export default function CustomerDetailClient({ customerName: propCustomerName }: { customerName: string }) {
  const { t } = useTranslation();
  const params = useParams();
  const router = useRouter();

  // Original state
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [navMode, setNavMode] = useState<'sidebar' | 'topnav'>('sidebar');
  const [customerName, setCustomerName] = useState<string>('');
  const [customerOrders, setCustomerOrders] = useState<Sale[]>([]);
  const [customerStats, setCustomerStats] = useState<CustomerStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<string>('All');
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'status'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // New state from modification
  const [activeTab, setActiveTab] = useState<'orders' | 'payments' | 'statement'>('orders');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<string>('');

  // New state for advance payment modal
  const [showAdvancePaymentModal, setShowAdvancePaymentModal] = useState(false);

  // Add new state for manual payment recording
  const [showManualPaymentModal, setShowManualPaymentModal] = useState(false);

  // Use the store hook safely
  const [customerPayments, setCustomerPayments] = useState<any[]>([]);
  const [recordPaymentFn, setRecordPaymentFn] = useState<any>(null);

  // Add state for advance payments
  const [customerAdvancePayments, setCustomerAdvancePayments] = useState<any[]>([]);

  // Navigation mode detection
  useEffect(() => {
    const savedNavMode = localStorage.getItem('navMode') as 'sidebar' | 'topnav' | null;
    if (savedNavMode) {
      setNavMode(savedNavMode);
    }
  }, []);

  // Initialize store functions - only run when customerName is available
  useEffect(() => {
    if (!customerName) return;

    try {
      const store = SalesStore.getInstance();
      setCustomerPayments(store.getPaymentsByCustomer(customerName) || []);
      setCustomerAdvancePayments(store.getAdvancePaymentsByCustomer(customerName) || []);
      setRecordPaymentFn(() => (paymentId: string, amount: number, method: any, notes?: string) => {
        try {
          store.recordPayment(paymentId, amount, method, notes);
          setCustomerPayments(store.getPaymentsByCustomer(customerName) || []);
        } catch (error) {
          console.error('Error recording payment:', error);
        }
      });
    } catch (error) {
      console.error('Error initializing store functions:', error);
      setCustomerPayments([]);
      setCustomerAdvancePayments([]);
    }
  }, [customerName]);

  // Modified effect with better error handling and customer name parsing
  useEffect(() => {
    try {
      // Safely extract customer name with proper URL decoding
      let name = '';
      if (propCustomerName) {
        name = decodeURIComponent(propCustomerName);
      } else if (params?.name) {
        name = decodeURIComponent(Array.isArray(params.name) ? params.name[0] : params.name);
      }

      if (!name || name.trim() === '') {
        setIsLoading(false);
        setCustomerStats(null);
        return;
      }

      setCustomerName(name.trim());

      const salesStore = SalesStore.getInstance();
      const allSales = salesStore.getSales() || [];

      // Filter orders for this customer with case-insensitive comparison
      const orders = allSales.filter(sale =>
        sale?.customer && sale.customer.toLowerCase() === name.toLowerCase()
      );

      // Load customer profile from localStorage with error handling
      let customerProfile = null;
      if (typeof window !== 'undefined') {
        try {
          const profilesData = localStorage.getItem('customerProfiles');
          if (profilesData) {
            const profiles = JSON.parse(profilesData);
            // Case-insensitive profile lookup
            customerProfile = profiles[name] ||
              Object.values(profiles).find((profile: any) =>
                profile?.name && profile.name.toLowerCase() === name.toLowerCase()
              );
          }
        } catch (error) {
          console.error('Error loading customer profiles:', error);
        }
      }

      // Initialize stats for new customers
      const currentDate = new Date().toISOString().split('T')[0];
      const emptyStats: CustomerStats = {
        totalOrders: 0,
        totalAmount: 0,
        totalCommission: 0,
        averageOrderValue: 0,
        firstOrderDate: customerProfile?.createdDate || currentDate,
        lastOrderDate: customerProfile?.createdDate || currentDate,
        status: 'Inactive',
      };

      if (orders.length === 0) {
        // Check if customer profile exists
        if (customerProfile) {
          // Customer exists in profiles but has no orders yet
          setCustomerOrders([]);
          setCustomerStats(emptyStats);
          setIsLoading(false);
          return;
        } else {
          // Customer doesn't exist at all - this will show the not found message
          setCustomerOrders([]);
          setCustomerStats(null);
          setIsLoading(false);
          return;
        }
      }

      // Calculate customer statistics when orders exist with error handling
      let totalAmount = 0;
      let totalCommission = 0;

      try {
        totalAmount = orders.reduce((sum, order) => {
          if (order?.total) {
            const amount = parseFloat(order.total.replace(/[$,]/g, ''));
            return sum + (isNaN(amount) ? 0 : amount);
          }
          return sum;
        }, 0);

        totalCommission = orders.reduce((sum, order) => {
          if (order?.commission) {
            const commission = parseFloat(order.commission.replace(/[$,]/g, ''));
            return sum + (isNaN(commission) ? 0 : commission);
          }
          return sum;
        }, 0);
      } catch (error) {
        console.error('Error calculating totals:', error);
        totalAmount = 0;
        totalCommission = 0;
      }

      // Safely calculate order dates
      let firstOrderDate = currentDate;
      let lastOrderDate = currentDate;

      try {
        const validOrders = orders.filter(order => order?.salesDate);
        if (validOrders.length > 0) {
          const orderDates = validOrders.map(order => new Date(order.salesDate));
          const validDates = orderDates.filter(date => !isNaN(date.getTime()));

          if (validDates.length > 0) {
            firstOrderDate = new Date(Math.min(...validDates.map(d => d.getTime()))).toISOString().split('T')[0];
            lastOrderDate = new Date(Math.max(...validDates.map(d => d.getTime()))).toISOString().split('T')[0];
          }
        }
      } catch (error) {
        console.error('Error calculating order dates:', error);
      }

      // Determine if customer is active (ordered within last 30 days)
      let status: 'Active' | 'Inactive' = 'Inactive';
      try {
        const daysSinceLastOrder = Math.floor(
          (new Date().getTime() - new Date(lastOrderDate).getTime()) / (1000 * 60 * 60 * 24)
        );
        status = daysSinceLastOrder <= 30 ? 'Active' : 'Inactive';
      } catch (error) {
        console.error('Error calculating status:', error);
      }

      const stats: CustomerStats = {
        totalOrders: orders.length,
        totalAmount,
        totalCommission,
        averageOrderValue: orders.length > 0 ? totalAmount / orders.length : 0,
        firstOrderDate,
        lastOrderDate,
        status,
      };

      setCustomerOrders(orders);
      setCustomerStats(stats);
      setIsLoading(false);

      // Subscribe to changes with error handling
      const unsubscribe = salesStore.subscribe(() => {
        try {
          const updatedSales = salesStore.getSales() || [];
          const updatedOrders = updatedSales.filter(sale =>
            sale?.customer && sale.customer.toLowerCase() === name.toLowerCase()
          );
          setCustomerOrders(updatedOrders);
          // Update payments when sales change
          setCustomerPayments(salesStore.getPaymentsByCustomer(name) || []);
          setCustomerAdvancePayments(salesStore.getAdvancePaymentsByCustomer(name) || []);
        } catch (error) {
          console.error('Error updating customer data:', error);
        }
      });

      return unsubscribe;
    } catch (error) {
      console.error('Error in customer detail effect:', error);
      setIsLoading(false);
      setCustomerStats(null);
    }
  }, [params.name, propCustomerName]);

  // Original filtered & sorted orders with safety checks
  const filteredOrders = (customerOrders || [])
    .filter(order => order && (statusFilter === 'All' || order.status === statusFilter))
    .sort((a, b) => {
      if (!a || !b) return 0;

      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'date':
          aValue = a.salesDate ? new Date(a.salesDate) : new Date(0);
          bValue = b.salesDate ? new Date(b.salesDate) : new Date(0);
          break;
        case 'amount':
          aValue = a.total ? parseFloat(a.total.replace(/[$,]/g, '')) : 0;
          bValue = b.total ? parseFloat(b.total.replace(/[$,]/g, '')) : 0;
          break;
        case 'status':
          aValue = a.status || '';
          bValue = b.status || '';
          break;
        default:
          aValue = a.salesDate ? new Date(a.salesDate) : new Date(0);
          bValue = b.salesDate ? new Date(b.salesDate) : new Date(0);
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

  const getStatusColor = (status: string) => {
    if (!status) return 'bg-gray-100 text-gray-800 border-gray-200';

    switch (status) {
      case 'Completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Processing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Shipped':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'Delivered':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Confirmed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTranslatedStatus = (status: string) => {
    if (!status) return status;

    switch (status) {
      case 'Completed':
        return t('sales.statuses.completed') || status;
      case 'Processing':
        return t('sales.statuses.processing') || status;
      case 'Shipped':
        return t('sales.statuses.shipped') || status;
      case 'Delivered':
        return t('sales.statuses.delivered') || status;
      case 'Pending':
        return t('sales.statuses.pending') || status;
      case 'Confirmed':
        return t('sales.statuses.confirmed') || status;
      case 'Cancelled':
        return t('sales.statuses.cancelled') || status;
      default:
        return status;
    }
  };

  const toggleSort = (column: 'date' | 'amount' | 'status') => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  // New payment handling with error handling
  const handleRecordPayment = (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const formData = new FormData(e.target as HTMLFormElement);
      const amount = parseFloat(formData.get('amount') as string);
      const method = formData.get('method') as any;
      const notes = formData.get('notes') as string;

      if (isNaN(amount) || amount <= 0) {
        alert('Please enter a valid payment amount');
        return;
      }

      if (recordPaymentFn && selectedPayment) {
        recordPaymentFn(selectedPayment, amount, method, notes);
      }
      setShowPaymentModal(false);
      setSelectedPayment('');
    } catch (error) {
      console.error('Error recording payment:', error);
      alert('Error recording payment. Please try again.');
    }
  };

  const getPaymentStatusColor = (status: string) => {
    if (!status) return 'bg-gray-100 text-gray-800 border-gray-200';

    switch (status) {
      case 'Paid':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Partial':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Pending':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Overdue':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTranslatedPaymentStatus = (status: string) => {
    if (!status) return status;

    switch (status) {
      case 'Paid':
        return t('payments.paid') || status;
      case 'Partial':
        return t('payments.partial') || status;
      case 'Pending':
        return t('payments.pending') || status;
      case 'Overdue':
        return t('payments.overdue') || status;
      case 'Advance Payment':
        return t('payments.advancePayment') || status;
      default:
        return status;
    }
  };

  // Safe calculations with fallbacks
  const totalOwed = (customerPayments || []).reduce((sum, p) => sum + (p?.remainingAmount || 0), 0);
  const totalPaidFromInvoices = (customerPayments || []).reduce((sum, p) => sum + (p?.paidAmount || 0), 0);
  const totalAdvancePayments = (customerAdvancePayments || []).reduce((sum, adv) => sum + (adv?.amount || 0), 0);
  const totalPaid = totalPaidFromInvoices + totalAdvancePayments;
  const totalRevenue = customerStats?.totalAmount ?? 0;

  // Calculate outstanding from sales orders that don't have payment records
  const salesOrdersTotal = (customerOrders || []).reduce((sum, order) => {
    if (order?.total) {
      const amount = parseFloat(order.total.replace(/[$,]/g, ''));
      return sum + (isNaN(amount) ? 0 : amount);
    }
    return sum;
  }, 0);

  // Outstanding should be sales total minus all payments (invoices + advances)
  const actualOutstanding = Math.max(0, salesOrdersTotal - totalPaid);

  // Create combined statement transactions from both orders and payments with safety checks
  const statementTransactions = [
    // Sales orders as debits (money owed by customer)
    ...(customerOrders || []).filter(order => order && order.id).map(order => ({
      id: `order-${order.id}`,
      date: order.salesDate || new Date().toISOString().split('T')[0],
      description: `${t('customerDetail.invoice') || 'Invoice'} - ${order.products?.length || 0} ${(order.products?.length || 0) === 1 ? t('sales.item') || 'item' : t('sales.items') || 'items'}`,
      details: `${t('sales.salesRep') || 'Sales Rep'}: ${order.salesRep || order.vendor || 'N/A'}`,
      referenceNumber: order.id,
      debitAmount: order.total ? parseFloat(order.total.replace(/[$,]/g, '')) || 0 : 0,
      creditAmount: 0,
      status: order.status || 'Unknown',
      type: 'invoice'
    })),

    // Invoice payments as credits (money received from customer)
    ...(customerPayments || []).flatMap(payment =>
      (payment?.payments || []).map((p: any) => ({
        id: `payment-${p?.id || Math.random()}`,
        date: p?.date || new Date().toISOString().split('T')[0],
        description: `${t('customerDetail.payment') || 'Payment'} - ${payment?.invoiceNumber || 'N/A'}`,
        details: `${t('customerDetail.paymentModal.paymentMethod') || 'Payment Method'}: ${p?.method || 'N/A'}${p?.notes ? ` - ${p.notes}` : ''}`,
        referenceNumber: payment?.invoiceNumber || 'N/A',
        debitAmount: 0,
        creditAmount: p?.amount || 0,
        status: 'Paid',
        type: 'payment'
      }))
    ),

    // Advance payments as credits
    ...(customerAdvancePayments || []).filter(advance => advance && advance.id).map(advance => ({
      id: `advance-${advance.id}`,
      date: advance.date || new Date().toISOString().split('T')[0],
      description: `${t('customerDetail.advancePayment') || 'Advance Payment'}`,
      details: `${t('customerDetail.paymentModal.paymentMethod') || 'Payment Method'}: ${advance.method || 'N/A'}${advance.notes ? ` - ${advance.notes}` : ''}`,
      referenceNumber: advance.referenceNumber || 'N/A',
      debitAmount: 0,
      creditAmount: advance.amount || 0,
      status: 'Advance Payment',
      type: 'advance'
    }))
  ].sort((a, b) => {
    try {
      return new Date(a.date).getTime() - new Date(b.date).getTime();
    } catch {
      return 0;
    }
  }); // Sort chronologically (oldest first)

  // Calculate running balance correctly for chronological display (oldest to newest)
  let runningBalance = 0;

  const transactionsWithBalance = statementTransactions.map((transaction) => {
    runningBalance += (transaction.debitAmount - transaction.creditAmount);
    return {
      ...transaction,
      balance: runningBalance
    };
  });

  const handleAdvancePayment = (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const formData = new FormData(e.target as HTMLFormElement);
      const amount = parseFloat(formData.get('amount') as string);
      const method = formData.get('method') as string;
      const notes = formData.get('notes') as string;

      if (isNaN(amount) || amount <= 0) {
        alert('Please enter a valid payment amount');
        return;
      }

      if (!method) {
        alert('Please select a payment method');
        return;
      }

      const salesStore = SalesStore.getInstance();
      salesStore.recordAdvancePayment(customerName, amount, method, notes);

      setCustomerAdvancePayments(salesStore.getAdvancePaymentsByCustomer(customerName) || []);
      setShowAdvancePaymentModal(false);
    } catch (error) {
      console.error('Error recording advance payment:', error);
      alert('Error recording advance payment. Please try again.');
    }
  };

  // Add new manual payment handler with error handling
  const handleManualPayment = (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const formData = new FormData(e.target as HTMLFormElement);
      const amount = parseFloat(formData.get('amount') as string);
      const method = formData.get('method') as string;
      const notes = formData.get('notes') as string;
      const reference = formData.get('reference') as string;

      if (isNaN(amount) || amount <= 0) {
        alert('Please enter a valid payment amount');
        return;
      }

      if (!method) {
        alert('Please select a payment method');
        return;
      }

      const salesStore = SalesStore.getInstance();

      const invoiceNumber = reference || `MAN-${new Date().getFullYear()}-${String(Date.now()).slice(-4)}`;

      const newPayment = {
        orderId: 'MANUAL-ENTRY',
        customerName: customerName,
        amount: amount,
        paidAmount: amount,
        remainingAmount: 0,
        paymentMethod: method as any,
        status: 'Paid' as any,
        dueDate: new Date().toISOString().split('T')[0],
        paidDate: new Date().toISOString().split('T')[0],
        invoiceNumber: invoiceNumber,
        notes: notes
      };

      salesStore.addPayment(newPayment);

      setCustomerPayments(salesStore.getPaymentsByCustomer(customerName) || []);
      setShowManualPaymentModal(false);
    } catch (error) {
      console.error('Error recording manual payment:', error);
      alert('Error recording manual payment. Please try again.');
    }
  };

  const generateStatementText = (data: any) => {
    try {
      return `
CUSTOMER STATEMENT
==================

Customer: ${data.customerName || 'N/A'}
Statement Date: ${data.statementDate || 'N/A'}
Current Balance: $${Math.abs(data.currentBalance || 0).toLocaleString()}

TRANSACTION HISTORY
===================
Date        Description                    Reference        Debit         Credit        Balance
${(data.transactions || []).map((t: any) =>
  `${(new Date(t.date || new Date()).toLocaleDateString() || '').padEnd(12)}${(t.description || '').substring(0, 30).padEnd(31)}${(t.referenceNumber || '').padEnd(17)}${(t.debitAmount > 0 ? `$${t.debitAmount.toLocaleString()}` : '').padEnd(14)}${(t.creditAmount > 0 ? `$${t.creditAmount.toLocaleString()}` : '').padEnd(14)}$${Math.abs(t.balance || 0).toLocaleString()}`
).join('\n')}

SUMMARY
=======
Total Debits: $${(data.summary?.totalDebits || 0).toLocaleString()}
Total Credits: $${(data.summary?.totalCredits || 0).toLocaleString()}
Final Balance: $${Math.abs(data.summary?.finalBalance || 0).toLocaleString()}
`;
    } catch (error) {
      console.error('Error generating statement text:', error);
      return 'Error generating statement';
    }
  };

  const generateStatementExcel = (data: any) => {
    try {
      let htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        table { 
            border-collapse: collapse; 
            width: 100%; 
            table-layout: fixed !important;
            margin-bottom: 20px;
        }
        th, td { 
            border: 1px solid #000; 
            padding: 8px; 
            text-align: left; 
            vertical-align: middle !important;
            word-wrap: break-word;
            overflow: hidden !important;
        }
        th { 
            background-color: #f2f2f2; 
            font-weight: bold; 
            height: 40px !important;
            text-align: center;
        }
        .header-section { 
            margin-bottom: 30px; 
            padding: 20px;
            background-color: #f8f9fa;
            border: 2px solid #007bff;
        }
        .section-title { 
            font-size: 18px; 
            font-weight: bold; 
            margin: 20px 0 15px 0; 
            color: #333;
            background-color: #e9ecef;
            padding: 10px;
            border-left: 5px solid #007bff;
        }
        .summary-section { 
            margin-top: 30px; 
            background-color: #f8f9fa; 
            padding: 20px; 
            border: 1px solid #dee2e6;
        }
        .amount-cell { text-align: right !important; font-weight: bold; }
        .debit-amount { color: #dc3545; }
        .credit-amount { color: #28a745; }
        .balance-amount { color: #6f42c1; font-weight: bold; }
        .date-cell { text-align: center !important; }
        .reference-cell { text-align: center !important; font-family: monospace; }
        .description-cell { padding-left: 12px !important; }
    </style>
</head>
<body>
    <div class="header-section">
        <h1 style="margin: 0 0 15px 0; color: #007bff; font-size: 24px;">CUSTOMER STATEMENT</h1>
        <table style="border: none; margin: 0;">
            <colgroup>
                <col width="150">
                <col width="200">
                <col width="150">
                <col width="200">
            </colgroup>
            <tr style="border: none;">
                <td style="border: none; font-weight: bold;">Customer:</td>
                <td style="border: none;">${data.customerName || 'N/A'}</td>
                <td style="border: none; font-weight: bold;">Statement Date:</td>
                <td style="border: none;">${data.statementDate || 'N/A'}</td>
            </tr>
            <tr style="border: none;">
                <td style="border: none; font-weight: bold;">Current Balance:</td>
                <td style="border: none; color: ${Math.abs(data.currentBalance || 0) > 0 ? '#dc3545' : '#28a745'}; font-weight: bold; font-size: 16px;">
                    $${Math.abs(data.currentBalance || 0).toLocaleString()}
                </td>
                <td style="border: none; font-weight: bold;">Generated:</td>
                <td style="border: none;">${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}</td>
            </tr>
        </table>
    </div>

    <div class="section-title">TRANSACTION HISTORY</div>
    <table>
        <colgroup>
            <col width="100">
            <col width="300">
            <col width="120">
            <col width="100">
            <col width="100">
            <col width="120">
        </colgroup>
        <thead>
            <tr>
                <th class="date-cell">Date</th>
                <th>Description</th>
                <th class="reference-cell">Reference</th>
                <th class="amount-cell">Debit</th>
                <th class="amount-cell">Credit</th>
                <th class="amount-cell">Balance</th>
            </tr>
        </thead>
        <tbody>`;

      (data.transactions || []).forEach((transaction: any) => {
        const transactionDate = new Date(transaction.date || new Date()).toLocaleDateString();
        const debitAmount = transaction.debitAmount > 0 ? `$${transaction.debitAmount.toLocaleString()}` : '';
        const creditAmount = transaction.creditAmount > 0 ? `$${transaction.creditAmount.toLocaleString()}` : '';
        const balanceAmount = `$${Math.abs(transaction.balance || 0).toLocaleString()}`;
        
        htmlContent += `
            <tr>
                <td class="date-cell">${transactionDate}</td>
                <td class="description-cell">
                    <strong>${transaction.description || ''}</strong>
                    ${transaction.details ? `<br><small style="color: #666;">${transaction.details}</small>` : ''}
                </td>
                <td class="reference-cell">${transaction.referenceNumber || 'N/A'}</td>
                <td class="amount-cell debit-amount">${debitAmount}</td>
                <td class="amount-cell credit-amount">${creditAmount}</td>
                <td class="amount-cell balance-amount">${balanceAmount}</td>
            </tr>`;
      });

      htmlContent += `
        </tbody>
    </table>

    <div class="summary-section">
        <div class="section-title" style="margin-top: 0;">STATEMENT SUMMARY</div>
        <table>
            <colgroup>
                <col width="300">
                <col width="150">
            </colgroup>
            <thead>
                <tr>
                    <th>Description</th>
                    <th class="amount-cell">Amount</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Total Debits (Invoices)</strong></td>
                    <td class="amount-cell debit-amount">$${(data.summary?.totalDebits || 0).toLocaleString()}</td>
                </tr>
                <tr>
                    <td><strong>Total Credits (Payments)</strong></td>
                    <td class="amount-cell credit-amount">$${(data.summary?.totalCredits || 0).toLocaleString()}</td>
                </tr>
                <tr style="border-top: 2px solid #000; background-color: #f8f9fa;">
                    <td><strong>Current Balance</strong></td>
                    <td class="amount-cell balance-amount" style="font-size: 16px;">
                        $${Math.abs(data.summary?.finalBalance || 0).toLocaleString()}
                    </td>
                </tr>
                <tr>
                    <td>Total Transactions</td>
                    <td class="amount-cell">${(data.transactions || []).length}</td>
                </tr>
                <tr>
                    <td>Statement Period</td>
                    <td class="amount-cell">All Time</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div style="margin-top: 30px; padding: 15px; background-color: #e9ecef; border-left: 5px solid #17a2b8;">
        <p style="margin: 0; font-size: 12px; color: #495057;">
            <strong>Statement Notes:</strong><br>
            • Debit amounts represent invoices and charges<br>
            • Credit amounts represent payments and refunds<br>
            • Balance shows running total after each transaction<br>
            • All amounts are in USD<br>
            • Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}
        </p>
    </div>
</body>
</html>`;

      return htmlContent;
    } catch (error) {
      console.error('Error generating Excel statement:', error);
      return '<html><body><h1>Error generating statement</h1></body></html>';
    }
  };

  const handleExportStatement = () => {
    try {
      const statementData = {
        customerName: customerName || 'Unknown Customer',
        statementDate: new Date().toLocaleDateString(),
        currentBalance: actualOutstanding,
        transactions: transactionsWithBalance,
        summary: {
          totalDebits: transactionsWithBalance.reduce((sum, t) => sum + (t.debitAmount || 0), 0),
          totalCredits: transactionsWithBalance.reduce((sum, t) => sum + (t.creditAmount || 0), 0),
          finalBalance: transactionsWithBalance.reduce((sum, t) => sum + ((t.debitAmount || 0) - (t.creditAmount || 0)), 0),
        },
      };

      // Generate Excel format
      const excelContent = generateStatementExcel(statementData);
      const blob = new Blob([excelContent], { type: 'application/vnd.ms-excel' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${customerName || 'customer'}_statement_${new Date().toISOString().split('T')[0]}.xls`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting statement:', error);
      alert('Error exporting statement. Please try again.');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
        <main className={`transition-all duration-300 p-6 ${navMode === 'topnav' ? '' : (sidebarOpen ? 'pl-64' : 'pl-16')}`}>
          <div className="max-w-7xl mx-auto animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4" />
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-8" />
            <div className="space-y-4">
              <div className="h-32 bg-gray-200 rounded" />
              <div className="h-48 bg-gray-200 rounded" />
            </div>
          </div>
        </main>
      </div>
    );
  }

  // Modified not-found handling for better customer experience
  if (!customerStats) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
        <main className={`transition-all duration-300 p-6 ${navMode === 'topnav' ? '' : (sidebarOpen ? 'pl-64' : 'pl-16')}`}>
          <div className="max-w-7xl mx-auto">
            <div className="text-center py-16">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 max-w-md mx-auto">
                <i className="ri-user-search-line w-16 h-16 flex items-center justify-center mx-auto text-gray-300 mb-4"></i>
                <h3 className="text-lg font-medium text-gray-900 mb-2">{t('customerDetail.customerNotFound') || 'Customer Not Found'}</h3>
                <p className="text-sm text-gray-500 mb-6">
                  The customer "{customerName || 'Unknown'}" doesn't exist in the system yet.
                </p>
                <div className="flex flex-col space-y-3">
                  <Link
                    href={`/sales/new?customer=${encodeURIComponent(customerName || '')}`}
                    className="inline-flex items-center justify-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap"
                  >
                    <i className="ri-add-line w-4 h-4 flex items-center justify-center"></i>
                    <span>Create First Order for {customerName || 'this customer'}</span>
                  </Link>
                  <Link
                    href="/customers"
                    className="inline-flex items-center justify-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap"
                  >
                    <i className="ri-arrow-left-line w-4 h-4 flex items-center justify-center"></i>
                    <span>{t('customerDetail.backToCustomers') || 'Back to Customers'}</span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
      <main className={`transition-all duration-300 p-6 ${navMode === 'topnav' ? '' : (sidebarOpen ? 'pl-64' : 'pl-16')}`}>
        <div className="max-w-7xl mx-auto">

          {/* Breadcrumb & Header (original) */}
          <div className="mb-6">
            <div className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
              <Link href="/customers" className="hover:text-gray-700 cursor-pointer">
                {t('nav.customers')}
              </Link>
              <i className="ri-arrow-right-s-line w-4 h-4 flex items-center justify-center"></i>
              <span className="text-gray-900 font-medium">{customerName}</span>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center space-x-3">
                  <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                    <span className="text-lg font-semibold text-blue-600">
                      {customerName.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <span>{customerName}</span>
                </h1>
                <p className="text-gray-600">{t('customerDetail.customerProfile')}</p>
              </div>

              {/* Updated actions and status */}
              <div className="flex items-center space-x-3">
                <Link
                  href={`/sales/new?customer=${encodeURIComponent(customerName)}`}
                  className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap transition-colors"
                >
                  <i className="ri-add-line w-4 h-4 flex items-center justify-center"></i>
                  <span>Create Sales Order</span>
                </Link>
                <span
                  className={`inline-flex items-center px-3 py-2 rounded-full text-sm font-medium border ${
                    customerStats.status === 'Active'
                      ? 'bg-green-100 text-green-800 border-green-200'
                      : 'bg-gray-100 text-gray-800 border-gray-200'
                  }`}
                >
                  <div
                    className={`w-2 h-2 rounded-full mr-2 ${
                      customerStats.status === 'Active' ? 'bg-green-500' : 'bg-gray-500'
                    }`}
                  ></div>
                  {customerStats.status === 'Active' ? t('customers.active') : t('customers.inactive')}
                </span>
              </div>
            </div>
          </div>

          {/* Stats Grid (merged) */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
            {/* Total Orders */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600">{t('customerDetail.totalOrders')}</p>
                  <p className="text-2xl font-bold text-blue-600">{customerOrders.length}</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <i className="ri-shopping-cart-line w-6 h-6 flex items-center justify-center text-blue-600"></i>
                </div>
              </div>
            </div>

            {/* Total Paid */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600">{t('customerDetail.totalPaid')}</p>
                  <p className="text-2xl font-bold text-green-600">${totalPaid.toLocaleString()}</p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <i className="ri-money-dollar-circle-line w-6 h-6 flex items-center justify-center text-green-600"></i>
                </div>
              </div>
            </div>

            {/* Outstanding */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600">{t('customerDetail.outstanding')}</p>
                  <p className="text-2xl font-bold text-red-600">${actualOutstanding.toLocaleString()}</p>
                </div>
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                  <i className="ri-alert-line w-6 h-6 flex items-center justify-center text-red-600"></i>
                </div>
              </div>
            </div>
          </div>

          {/* Timeline (original) */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('customerDetail.customerTimeline')}</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <i className="ri-calendar-check-line w-5 h-5 flex items-center justify-center text-blue-600"></i>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">{t('customerDetail.firstOrder')}</p>
                  <p className="text-base font-semibold text-gray-900">
                    {new Date(customerStats.firstOrderDate).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <i className="ri-calendar-event-line w-5 h-5 flex items-center justify-center text-green-600"></i>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">{t('customerDetail.lastOrder')}</p>
                  <p className="text-base font-semibold text-gray-900">
                    {new Date(customerStats.lastOrderDate).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Empty State for New Customers */}
          {customerOrders.length === 0 && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-6">
              <div className="text-center">
                <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i className="ri-shopping-cart-line w-10 h-10 flex items-center justify-center text-blue-600"></i>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Welcome, {customerName}!</h3>
                <p className="text-gray-600 mb-6">This customer doesn't have any orders yet. Create their first order to get started.</p>
                <div className="flex justify-center space-x-4">
                  <Link
                    href={`/sales/new?customer=${encodeURIComponent(customerName)}`}
                    className="inline-flex items-center space-x-2 px-6 py-3 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap"
                  >
                    <i className="ri-add-line w-4 h-4 flex items-center justify-center"></i>
                    <span>Create First Order</span>
                  </Link>
                  <button
                    onClick={() => setShowAdvancePaymentModal(true)}
                    className="inline-flex items-center space-x-2 px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap"
                  >
                    <i className="ri-money-dollar-circle-line w-4 h-4 flex items-center justify-center"></i>
                    <span>Record Advance Payment</span>
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Orders & Payments Section - Only show if there are orders or payments */}
          {(customerOrders.length > 0 || customerPayments.length > 0 || customerAdvancePayments.length > 0) && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-6">
                    <button
                      onClick={() => setActiveTab('orders')}
                      className={`text-lg font-semibold pb-2 border-b-2 transition-colors ${
                        activeTab === 'orders'
                          ? 'text-blue-600 border-blue-600'
                          : 'text-gray-400 border-transparent hover:text-gray-600'
                      }`}
                    >
                      {t('customerDetail.orderHistory')} ({customerOrders.length})
                    </button>
                    <button
                      onClick={() => setActiveTab('payments')}
                      className={`text-lg font-semibold pb-2 border-b-2 transition-colors ${
                        activeTab === 'payments'
                          ? 'text-blue-600 border-blue-600'
                          : 'text-gray-400 border-transparent hover:text-gray-600'
                      }`}
                    >
                      {t('customerDetail.paymentTracking')} ({customerPayments.length})
                    </button>
                    <button
                      onClick={() => setActiveTab('statement')}
                      className={`text-lg font-semibold pb-2 border-b-2 transition-colors ${
                        activeTab === 'statement'
                          ? 'text-blue-600 border-blue-600'
                          : 'text-gray-400 border-transparent hover:text-gray-600'
                      }`}
                    >
                      {t('customerDetail.customerStatement')} ({transactionsWithBalance.length})
                    </button>
                  </div>
                </div>

                {/* Tab Controls */}
                {activeTab === 'orders' && (
                  <div className="flex items-center space-x-4">
                    <select
                      value={statusFilter}
                      onChange={e => setStatusFilter(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-8"
                    >
                      <option value="All">{t('customerDetail.allStatus')}</option>
                      <option value="Completed">{t('sales.statuses.completed')}</option>
                      <option value="Processing">{t('sales.statuses.processing')}</option>
                      <option value="Shipped">{t('sales.statuses.shipped')}</option>
                      <option value="Delivered">{t('sales.statuses.delivered')}</option>
                      <option value="Pending">{t('sales.statuses.pending')}</option>
                      <option value="Confirmed">{t('sales.statuses.confirmed')}</option>
                      <option value="Cancelled">{t('sales.statuses.cancelled')}</option>
                    </select>
                    <div className="text-sm text-gray-500">{t('customerDetail.sortBy')}</div>
                    <button
                      onClick={() => toggleSort('date')}
                      className={`flex items-center space-x-1 px-3 py-2 text-sm font-medium rounded-lg cursor-pointer ${
                        sortBy === 'date' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      <span>{t('common.date')}</span>
                      {sortBy === 'date' && (
                        <i
                          className={`w-4 h-4 flex items-center justify-center ${
                            sortOrder === 'asc' ? 'ri-arrow-up-line' : 'ri-arrow-down-line'
                          }`}
                        ></i>
                      )}
                    </button>
                    <button
                      onClick={() => toggleSort('amount')}
                      className={`flex items-center space-x-1 px-3 py-2 text-sm font-medium rounded-lg cursor-pointer ${
                        sortBy === 'amount' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover-bg-gray-100'
                      }`}
                    >
                      <span>{t('common.amount')}</span>
                      {sortBy === 'amount' && (
                        <i
                          className={`w-4 h-4 flex items-center justify-center ${
                            sortOrder === 'asc' ? 'ri-arrow-up-line' : 'ri-arrow-down-line'
                          }`}
                        ></i>
                      )}
                    </button>
                    <button
                      onClick={() => toggleSort('status')}
                      className={`flex items-center space-x-1 px-3 py-2 text-sm font-medium rounded-lg cursor-pointer ${
                        sortBy === 'status' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      <span>{t('common.status')}</span>
                      {sortBy === 'status' && (
                        <i
                          className={`w-4 h-4 flex items-center justify-center ${
                            sortOrder === 'asc' ? 'ri-arrow-up-line' : 'ri-arrow-down-line'
                          }`}
                        ></i>
                      )}
                    </button>
                  </div>
                )}

                {activeTab === 'payments' && (
                  <div className="flex items-center space-x-4">
                    <select className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-8">
                      <option value="All">{t('customerDetail.allStatus')}</option>
                      <option value="Paid">{t('payments.paid')}</option>
                      <option value="Partial">{t('payments.partial')}</option>
                      <option value="Pending">{t('payments.pending')}</option>
                      <option value="Overdue">{t('payments.overdue')}</option>
                      <option value="Advance Payment">{t('payments.advancePayment')}</option>
                    </select>
                    <div className="text-sm text-gray-500">{t('customerDetail.sortBy')}</div>
                    <button className="flex items-center space-x-1 px-3 py-2 text-sm font-medium rounded-lg cursor-pointer bg-blue-100 text-blue-700">
                      <span>{t('customerDetail.dueDate')}</span>
                      <i className="w-4 h-4 flex items-center justify-center ri-arrow-down-line"></i>
                    </button>
                    <button className="flex items-center space-x-1 px-3 py-2 text-sm font-medium rounded-lg cursor-pointer text-gray-600 hover:bg-gray-100">
                      <span>{t('common.amount')}</span>
                    </button>
                  </div>
                )}

                {activeTab === 'statement' && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="text-sm text-gray-500">{t('customerDetail.statementPeriod')}</div>
                      <div className="text-sm text-gray-500">•</div>
                      <div className="text-sm font-medium text-gray-900">
                        {t('customerDetail.currentBalance')}: <span className={actualOutstanding > 0 ? 'text-red-600' : 'text-green-600'}>
                          ${Math.abs(actualOutstanding).toLocaleString()}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => setShowAdvancePaymentModal(true)}
                        className="px-4 py-2 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 cursor-pointer whitespace-nowrap flex items-center space-x-2"
                      >
                        <i className="ri-money-dollar-circle-line w-4 h-4 flex items-center justify-center"></i>
                        <span>{t('customerDetail.receiveMoney')}</span>
                      </button>
                      <button
                        onClick={() => setShowManualPaymentModal(true)}
                        className="px-4 py-2 text-sm bg-purple-600 text-white rounded-lg hover:bg-purple-700 cursor-pointer whitespace-nowrap flex items-center space-x-2"
                      >
                        <i className="ri-add-circle-line w-4 h-4 flex items-center justify-center"></i>
                        <span>Record Payment</span>
                      </button>
                      <button
                        onClick={handleExportStatement}
                        className="px-4 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap flex items-center space-x-2"
                      >
                        <i className="ri-download-line w-4 h-4 flex items-center justify-center"></i>
                        <span>{t('customerDetail.exportStatement')}</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>

              <div className="overflow-x-auto">
                {activeTab === 'orders' && (
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('customerDetail.tableHeaders.orderId')}
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('customerDetail.tableHeaders.salesRep')}
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('customerDetail.tableHeaders.date')}
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('customerDetail.tableHeaders.products')}
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('customerDetail.tableHeaders.total')}
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('customerDetail.tableHeaders.status')}
                        </th>
                        <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('customerDetail.tableHeaders.actions')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredOrders.map(order => (
                        <tr key={order.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Link href={`/sales/${order.id}`}>
                              <span className="text-sm font-semibold text-blue-600 hover:text-blue-800 cursor-pointer">
                                {order.id}
                              </span>
                            </Link>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">{order.salesRep || order.vendor}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center space-x-2">
                              <i className="ri-calendar-line w-4 h-4 flex items-center justify-center text-gray-400"></i>
                              <span className="text-sm text-gray-700">
                                {new Date(order.salesDate).toLocaleDateString()}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {order.products.length} {order.products.length === 1 ? t('sales.item') : t('sales.items')}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-semibold text-green-600">{order.total}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(
                                order.status
                              )}`}
                            >
                              <div
                                className={`w-2 h-2 rounded-full mr-2 ${
                                  order.status === 'Completed'
                                    ? 'bg-green-500'
                                    : order.status === 'Processing'
                                    ? 'bg-blue-500'
                                    : order.status === 'Shipped'
                                    ? 'bg-purple-500'
                                    : order.status === 'Delivered'
                                    ? 'bg-gray-500'
                                    : order.status === 'Pending'
                                    ? 'bg-yellow-500'
                                    : order.status === 'Confirmed'
                                    ? 'bg-blue-500'
                                    : 'bg-red-500'
                                }`}
                              ></div>
                              {getTranslatedStatus(order.status)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center">
                            <Link href={`/sales/${order.id}`}>
                              <button className="text-blue-600 hover:text-blue-800 font-medium text-sm cursor-pointer whitespace-nowrap">
                                {t('customerDetail.viewDetails')}
                              </button>
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}

                {activeTab === 'payments' && (
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('customerDetail.type')}
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('customerDetail.reference')}
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('customerDetail.tableHeaders.orderId')}
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('customerDetail.amount')}
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('customerDetail.paid')}
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('customerDetail.outstanding')}
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('common.date')}
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('common.status')}
                        </th>
                        <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('common.actions')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {/* Regular Invoice Payments */}
                      {customerPayments.map(payment => (
                        <tr key={payment.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {t('customerDetail.invoice')}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm font-semibold text-blue-600">{payment.invoiceNumber}</span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Link href={`/sales/${payment.orderId}`}>
                              <span className="text-sm text-blue-600 hover:text-blue-800 cursor-pointer">
                                {payment.orderId}
                              </span>
                            </Link>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-semibold text-gray-900">
                              ${payment.amount.toLocaleString()}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-semibold text-green-600">
                              ${payment.paidAmount.toLocaleString()}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div
                              className={`text-sm font-semibold ${
                                payment.remainingAmount > 0 ? 'text-red-600' : 'text-green-600'
                              }`}
                            >
                              ${payment.remainingAmount.toLocaleString()}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center space-x-2">
                              <i className="ri-calendar-line w-4 h-4 flex items-center justify-center text-gray-400"></i>
                              <span className="text-sm text-gray-700">
                                {new Date(payment.dueDate).toLocaleDateString()}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getPaymentStatusColor(
                                payment.status
                              )}`}
                            >
                              <div
                                className={`w-2 h-2 rounded-full mr-2 ${
                                  payment.status === 'Paid'
                                    ? 'bg-green-500'
                                    : payment.status === 'Partial'
                                    ? 'bg-yellow-500'
                                    : payment.status === 'Pending'
                                    ? 'bg-blue-500'
                                    : 'bg-red-500'
                                }`}
                              ></div>
                              {getTranslatedPaymentStatus(payment.status)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center">
                            {payment.remainingAmount > 0 && (
                              <button
                                onClick={() => {
                                  setSelectedPayment(payment.id);
                                  setShowPaymentModal(true);
                                }}
                                className="text-blue-600 hover:text-blue-800 font-medium text-sm cursor-pointer whitespace-nowrap"
                              >
                                {t('customerDetail.recordPayment')}
                              </button>
                            )}
                          </td>
                        </tr>
                      ))}

                      {/* Advance Payments */}
                      {customerAdvancePayments.map(advance => (
                        <tr key={`advance-${advance.id}`} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                              {t('customerDetail.advance')}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm font-semibold text-blue-600">{advance.referenceNumber}</span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm text-gray-500">-</span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-semibold text-gray-900">
                              ${advance.amount.toLocaleString()}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-semibold text-green-600">
                              ${advance.amount.toLocaleString()}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-semibold text-green-600">
                              $0
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center space-x-2">
                              <i className="ri-calendar-line w-4 h-4 flex items-center justify-center text-gray-400"></i>
                              <span className="text-sm text-gray-700">
                                {new Date(advance.date).toLocaleDateString()}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border bg-purple-100 text-purple-800 border-purple-200">
                              <div className="w-2 h-2 rounded-full mr-2 bg-purple-500"></div>
                              {t('customerDetail.advancePayment')}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center">
                            <span className="text-sm text-gray-500">-</span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}

                {activeTab === 'statement' && (
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('common.date')}
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('customerDetail.description')}
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('customerDetail.referenceNumber')}
                        </th>
                        <th className="px-6 py-4 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('customerDetail.debitAmount')}
                        </th>
                        <th className="px-6 py-4 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('customerDetail.creditAmount')}
                        </th>
                        <th className="px-6 py-4 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('customerDetail.balance')}
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {t('common.status')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {transactionsWithBalance.map((transaction) => (
                        <tr key={transaction.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center space-x-2">
                              <i className="ri-calendar-line w-4 h-4 flex items-center justify-center text-gray-400"></i>
                              <span className="text-sm text-gray-700">
                                {new Date(transaction.date).toLocaleDateString()}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="text-sm font-medium text-gray-900">{transaction.description}</div>
                            <div className="text-xs text-gray-500">{transaction.details}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Link href={transaction.description.includes(t('customerDetail.invoice')) ? `/sales/${transaction.referenceNumber}` : '#'}>
                              <span className="text-sm text-blue-600 hover:text-blue-800 cursor-pointer">
                                {transaction.referenceNumber}
                              </span>
                            </Link>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right">
                            <div className="text-sm font-semibold text-red-600">
                              {transaction.debitAmount > 0 ? `$${transaction.debitAmount.toLocaleString()}` : '$0.00'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right">
                            <div className="text-sm font-semibold text-green-600">
                              {transaction.creditAmount > 0 ? `$${transaction.creditAmount.toLocaleString()}` : '$0.00'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right">
                            <div className={`text-sm font-bold ${
                              transaction.balance > 0 ? 'text-red-600' : transaction.balance < 0 ? 'text-green-600' : 'text-gray-600'
                            }`}>
                              ${Math.abs(transaction.balance).toLocaleString()}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${
                              transaction.description.includes(t('customerDetail.invoice')) ? getStatusColor(transaction.status) : getPaymentStatusColor(transaction.status)
                            }`}>
                              {transaction.description.includes(t('customerDetail.invoice')) ? getTranslatedStatus(transaction.status) : getTranslatedPaymentStatus(transaction.status)}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            </div>
          )}

          {/* Payment Modal */}
          {showPaymentModal && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">{t('customerDetail.paymentModal.title')}</h3>
                  <button
                    onClick={() => setShowPaymentModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
                  </button>
                </div>

                <form onSubmit={handleRecordPayment} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('customerDetail.paymentModal.paymentAmount')}
                    </label>
                    <input
                      type="number"
                      name="amount"
                      step="0.01"
                      min="0.01"
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('customerDetail.paymentModal.paymentMethod')}
                    </label>
                    <select
                      name="method"
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 pr-8"
                    >
                      <option value="">{t('customerDetail.paymentModal.selectMethod')}</option>
                      <option value="Cash">{t('customerDetail.paymentModal.cash')}</option>
                      <option value="Credit Card">{t('customerDetail.paymentModal.creditCard')}</option>
                      <option value="Bank Transfer">{t('customerDetail.paymentModal.bankTransfer')}</option>
                      <option value="Check">{t('customerDetail.paymentModal.check')}</option>
                      <option value="PayPal">{t('customerDetail.paymentModal.paypal')}</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('customerDetail.paymentModal.notesOptional')}
                    </label>
                    <textarea
                      name="notes"
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder={t('customerDetail.paymentModal.paymentNotes')}
                    ></textarea>
                  </div>

                  <div className="flex items-center justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowPaymentModal(false)}
                      className="px-4 py-2 text-gray-770 hover:text-gray-900 cursor-pointer whitespace-nowrap"
                    >
                      {t('customerDetail.paymentModal.cancel')}
                    </button>
                    <button
                      type="submit"
                      className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap"
                    >
                      {t('customerDetail.paymentModal.recordPaymentBtn')}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}

          {/* Advance Payment Modal */}
          {showAdvancePaymentModal && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">{t('customerDetail.advancePaymentModal.title')}</h3>
                  <button
                    onClick={() => setShowAdvancePaymentModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
                  </button>
                </div>

                <div className="mb-4">
                  <p className="text-sm text-gray-600">{t('sales.customer')}: {customerName}</p>
                  <p className="text-sm text-gray-600">{t('customerDetail.currentBalance')}: ${Math.abs(actualOutstanding).toLocaleString()}</p>
                </div>

                <form onSubmit={handleAdvancePayment} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('customerDetail.paymentModal.paymentAmount')}
                    </label>
                    <input
                      type="number"
                      name="amount"
                      step="0.01"
                      min="0.01"
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('customerDetail.paymentModal.paymentMethod')}
                    </label>
                    <select
                      name="method"
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 pr-8"
                    >
                      <option value="">{t('customerDetail.paymentModal.selectMethod')}</option>
                      <option value="Cash">{t('customerDetail.paymentModal.cash')}</option>
                      <option value="Credit Card">{t('customerDetail.paymentModal.creditCard')}</option>
                      <option value="Bank Transfer">{t('customerDetail.paymentModal.bankTransfer')}</option>
                      <option value="Check">{t('customerDetail.paymentModal.check')}</option>
                      <option value="PayPal">{t('customerDetail.paymentModal.paypal')}</option>
                    </select>
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('customerDetail.paymentModal.notesOptional')}
                    </label>
                    <textarea
                      name="notes"
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder={t('customerDetail.advancePaymentModal.advancePaymentNotes')}
                    ></textarea>
                  </div>

                  <div className="flex items-center justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowAdvancePaymentModal(false)}
                      className="px-4 py-2 text-gray-770 hover:text-gray-900 cursor-pointer whitespace-nowrap"
                    >
                      {t('customerDetail.paymentModal.cancel')}
                    </button>
                    <button
                      type="submit"
                      className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 cursor-pointer whitespace-nowrap"
                    >
                      {t('customerDetail.advancePaymentModal.receivePaymentBtn')}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}

          {/* Manual Payment Modal */}
          {showManualPaymentModal && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Record Manual Payment</h3>
                  <button
                    onClick={() => setShowManualPaymentModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
                  </button>
                </div>

                <div className="mb-4">
                  <p className="text-sm text-gray-600">{t('sales.customer')}: {customerName}</p>
                  <p className="text-sm text-gray-600">{t('customerDetail.currentBalance')}: ${Math.abs(actualOutstanding).toLocaleString()}</p>
                  <p className="text-xs text-blue-600 mt-1">Use this for payments made outside the system (e.g., Excel invoices, direct transfers)</p>
                </div>

                <form onSubmit={handleManualPayment} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Payment Amount *
                    </label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">$</span>
                      <input
                        type="number"
                        name="amount"
                        step="0.01"
                        min="0.01"
                        required
                        className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                        placeholder="0.00"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Reference Number
                    </label>
                    <input
                      type="text"
                      name="reference"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                      placeholder="Invoice #, Receipt #, etc. (optional)"
                    />
                    <p className="mt-1 text-xs text-gray-500">Leave blank to auto-generate</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Payment Method *
                    </label>
                    <select
                      name="method"
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 pr-8"
                    >
                      <option value="">Select method</option>
                      <option value="Cash">Cash</option>
                      <option value="Credit Card">Credit Card</option>
                      <option value="Bank Transfer">Bank Transfer</option>
                      <option value="Check">Check</option>
                      <option value="PayPal">PayPal</option>
                      <option value="Wire Transfer">Wire Transfer</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Notes
                    </label>
                    <textarea
                      name="notes"
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                      placeholder="Payment details, source, or additional information..."
                    ></textarea>
                  </div>

                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                    <div className="flex items-start space-x-2">
                      <i className="ri-information-line w-4 h-4 flex items-center justify-center text-yellow-600 mt-0.5"></i>
                      <div className="text-xs text-yellow-700">
                        <p className="font-medium">Manual Payment Recording</p>
                        <p>This will record a payment that was made outside the system. Use this for Excel invoices, direct bank transfers, or other external payments.</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowManualPaymentModal(false)}
                      className="px-4 py-2 text-gray-770 hover:text-gray-900 cursor-pointer whitespace-nowrap"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 cursor-pointer whitespace-nowrap flex items-center space-x-2"
                    >
                      <i className="ri-save-line w-4 h-4 flex items-center justify-center"></i>
                      <span>Record Payment</span>
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}

        </div>
      </main>
    </div>
  );
}
