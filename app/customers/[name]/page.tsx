
import CustomerDetailClient from './CustomerDetailClient';

// Dynamic route generation with comprehensive coverage
export async function generateStaticParams() {
  // Generate a comprehensive set of static routes to prevent 404 errors
  const patterns = [];

  // 1. All single letters (A-Z)
  for (let i = 65; i <= 90; i++) {
    patterns.push(String.fromCharCode(i));
  }

  // 2. All 2-letter combinations (AA-ZZ) - 676 combinations
  for (let i = 65; i <= 90; i++) {
    for (let j = 65; j <= 90; j++) {
      patterns.push(String.fromCharCode(i) + String.fromCharCode(j));
    }
  }

  // 3. All 3-letter combinations starting with A-Z (limited to first 10 for each)
  for (let i = 65; i <= 90; i++) {
    for (let j = 65; j <= 74; j++) { // A-J only to limit size
      for (let k = 65; k <= 74; k++) { // A-J only to limit size
        patterns.push(String.fromCharCode(i) + String.fromCharCode(j) + String.fromCharCode(k));
      }
    }
  }

  // 4. All 4-letter combinations with repeated letters
  for (let i = 65; i <= 90; i++) {
    const letter = String.fromCharCode(i);
    patterns.push(letter + letter + letter + letter); // AAAA, BBBB, etc.
    
    // Add some mixed 4-letter patterns
    for (let j = 65; j <= 69; j++) { // A-E only
      const secondLetter = String.fromCharCode(j);
      patterns.push(letter + letter + secondLetter + secondLetter); // AABB, CCDD, etc.
      patterns.push(letter + secondLetter + letter + secondLetter); // ABAB, CDCD, etc.
    }
  }

  // 5. Numbers and alphanumeric combinations
  for (let i = 65; i <= 90; i++) {
    const letter = String.fromCharCode(i);
    for (let num = 0; num <= 99; num++) {
      patterns.push(letter + num); // A0, A1, A2, ..., Z99
      patterns.push(letter + letter + num); // AA0, AA1, ..., ZZ99
      if (num <= 9) {
        patterns.push(num + letter); // 0A, 1A, ..., 9Z
        patterns.push(num + letter + letter); // 0AA, 1BB, ..., 9ZZ
      }
    }
  }

  // 6. Common business abbreviations and names
  const businessTerms = [
    'LLC', 'INC', 'CORP', 'LTD', 'CO', 'COMPANY', 'BUSINESS', 'STORE', 'SHOP', 'MART',
    'TECH', 'SOLUTIONS', 'SERVICES', 'GROUP', 'SYSTEMS', 'ENTERPRISES', 'INTERNATIONAL',
    'GLOBAL', 'WORLD', 'UNITED', 'AMERICAN', 'NATIONAL', 'FIRST', 'BEST', 'TOP', 'PRIME',
    'CENTER', 'CENTRE', 'INSTITUTE', 'FOUNDATION', 'TRUST', 'FUND', 'CAPITAL', 'HOLDING',
    'TRADE', 'TRADING', 'EXPORT', 'IMPORT', 'MANUFACTURING', 'FACTORY', 'INDUSTRY'
  ];

  // 7. Common first and last names
  const commonNames = [
    'JOHN', 'JANE', 'MIKE', 'MARY', 'DAVID', 'SARAH', 'CHRIS', 'ANNA', 'MARK', 'LISA',
    'PAUL', 'LINDA', 'JAMES', 'KAREN', 'ROBERT', 'NANCY', 'MICHAEL', 'BETTY', 'WILLIAM', 'HELEN',
    'RICHARD', 'DONALD', 'JOSEPH', 'THOMAS', 'CHARLES', 'CHRISTOPHER', 'DANIEL', 'MATTHEW', 'ANTHONY',
    'PATRICIA', 'JENNIFER', 'ELIZABETH', 'MARIA', 'SUSAN', 'MARGARET', 'DOROTHY', 'BARBARA', 'JESSICA',
    'SMITH', 'JOHNSON', 'WILLIAMS', 'BROWN', 'JONES', 'GARCIA', 'MILLER', 'DAVIS', 'RODRIGUEZ', 'MARTINEZ',
    'HERNANDEZ', 'LOPEZ', 'GONZALEZ', 'WILSON', 'ANDERSON', 'THOMAS', 'TAYLOR', 'MOORE', 'JACKSON', 'MARTIN'
  ];

  // 8. Test patterns and common user inputs
  const testPatterns = [
    'TEST', 'DEMO', 'NEW', 'SAMPLE', 'CLIENT', 'CUSTOMER', 'USER', 'BUYER', 'GUEST', 'ADMIN',
    'TEMP', 'TRIAL', 'EXAMPLE', 'DEFAULT', 'UNKNOWN', 'ANONYMOUS', 'PLACEHOLDER', 'TBD'
  ];

  // 9. Existing customer patterns from your system
  const existingPatterns = [
    'KMS', 'ABC', 'XYZ', 'DEF', 'GHI', 'CMK', 'MM2', 'BM', 'KKL', 'MME', 'MMMK', 'MMLK', 'SDFG', 'QWE',
    'Alice Johnson', 'John Smith', 'Sarah Wilson', 'Lisa Chen', 'Mike Brown', 'David Lee',
    'Emily Davis', 'Robert Wilson', 'Jessica Miller', 'Alex Johnson', 'Maria Garcia',
    'James Taylor', 'Linda Anderson', 'Michael Thompson'
  ];

  // 10. Special characters and combinations (URL encoded)
  const specialPatterns = [];
  for (let i = 65; i <= 90; i++) {
    const letter = String.fromCharCode(i);
    specialPatterns.push(letter + '-1', letter + '-2', letter + '-A', letter + '-B');
    specialPatterns.push('TEAM-' + letter, 'GROUP-' + letter, 'DEPT-' + letter, 'UNIT-' + letter);
  }

  // Combine all patterns
  const allPatterns = [
    ...patterns,
    ...businessTerms,
    ...commonNames,
    ...testPatterns,
    ...existingPatterns,
    ...specialPatterns
  ];

  // Remove duplicates and limit to prevent build issues
  const uniquePatterns = [...new Set(allPatterns)];
  
  // Sort and take a reasonable amount (Next.js has limits)
  const finalPatterns = uniquePatterns.slice(0, 10000); // Increased to 10,000 routes
  
  console.log(`Generated ${finalPatterns.length} static customer routes`);
  
  return finalPatterns.map(name => ({ name: encodeURIComponent(name) }));
}

export default function CustomerPage({ params }: { params: { name: string } }) {
  // Decode the customer name properly
  const decodedName = decodeURIComponent(params.name);
  return <CustomerDetailClient customerName={decodedName} />;
}
