
'use client';

import { useState } from 'react';

export default function OrderTable() {
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const [editingStatus, setEditingStatus] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'list' | 'card' | 'kanban'>('list');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [filters, setFilters] = useState({
    status: '',
    vendor: '',
    customer: '',
    dateFrom: '',
    dateTo: '',
    minAmount: '',
    maxAmount: ''
  });

  const [orders] = useState([
    { id: 'P00028', vendor: 'Ready Mat', customer: 'ABC Corp', expectedArrival: 'Dec 15, 2024', total: '$600.00', downPayment: '$180.00', status: 'Pending' },
    { id: 'P00027', vendor: 'Ready Mat', customer: 'XYZ Industries', expectedArrival: 'Dec 18, 2024', total: '$500.00', downPayment: '$150.00', status: 'In Processing' },
    { id: 'P00026', vendor: 'Abigail <PERSON>', customer: 'Tech Solutions', expectedArrival: 'Dec 20, 2024', total: '$115.00', downPayment: '$34.50', status: 'Shipped' },
    { id: 'P00025', vendor: 'Ready Mat', customer: 'Global Systems', expectedArrival: 'Dec 22, 2024', total: '$500.00', downPayment: '$150.00', status: 'Delivered' },
    { id: 'P00024', vendor: 'Petronyx Fuels', customer: 'Energy Corp', expectedArrival: 'Dec 25, 2024', total: '$1,000.00', downPayment: '$300.00', status: 'Pending' },
    { id: 'P00023', vendor: 'Voltixa', customer: 'Power Solutions', expectedArrival: 'Dec 28, 2024', total: '$1,000.00', downPayment: '$300.00', status: 'In Processing' },
    { id: 'P00022', vendor: 'Ready Mat', customer: 'Construction Inc', expectedArrival: 'Jan 2, 2025', total: '$172,500.00', downPayment: '$51,750.00', status: 'Shipped' },
    { id: 'P00021', vendor: 'Ready Mat', customer: 'Build Corp', expectedArrival: 'Jan 5, 2025', total: '$172,500.00', downPayment: '$51,750.00', status: 'Delivered' },
    { id: 'P00020', vendor: 'Wood Corner', customer: 'Interior Design Co', expectedArrival: 'Jan 8, 2025', total: '$862.50', downPayment: '$258.75', status: 'Pending' },
    { id: 'P00019', vendor: 'Wood Corner', customer: 'Home Decor Ltd', expectedArrival: 'Jan 10, 2025', total: '$46.00', downPayment: '$13.80', status: 'In Processing' },
    { id: 'P00018', vendor: 'Wood Corner', customer: 'Furniture Plus', expectedArrival: 'Jan 12, 2025', total: '$276.00', downPayment: '$82.80', status: 'Shipped' },
    { id: 'P00017', vendor: 'YourCompany, Joel Willis', customer: 'Retail Chain', expectedArrival: 'Jan 15, 2025', total: '$57.50', downPayment: '$17.25', status: 'Delivered' },
    { id: 'P00016', vendor: 'Ready Mat', customer: 'Manufacturing Co', expectedArrival: 'Jan 18, 2025', total: '$7,976.40', downPayment: '$2,392.92', status: 'Pending' },
    { id: 'P00015', vendor: 'Deco Addict', customer: 'Design Studio', expectedArrival: 'Jan 20, 2025', total: '$552.00', downPayment: '$165.60', status: 'In Processing' },
    { id: 'P00014', vendor: 'Wood Corner', customer: 'Office Solutions', expectedArrival: 'Jan 22, 2025', total: '$575.00', downPayment: '$172.50', status: 'Shipped' },
    { id: 'P00013', vendor: 'Wood Corner', customer: 'Workspace Inc', expectedArrival: 'Jan 25, 2025', total: '$690.00', downPayment: '$207.00', status: 'Delivered' },
    { id: 'P00012', vendor: 'Wood Corner', customer: 'Commercial Spaces', expectedArrival: 'Jan 28, 2025', total: '$1,725.00', downPayment: '$517.50', status: 'Pending' },
    { id: 'P00011', vendor: 'Ready Mat', customer: 'Hotel Group', expectedArrival: 'Jan 30, 2025', total: '$2,875.00', downPayment: '$862.50', status: 'In Processing' },
    { id: 'P00010', vendor: 'Azure Interior', customer: 'Restaurant Chain', expectedArrival: 'Feb 2, 2025', total: '$2,880.75', downPayment: '$864.23', status: 'Shipped' },
    { id: 'P00009', vendor: 'Gemini Furniture', customer: 'Hospitality Group', expectedArrival: 'Feb 5, 2025', total: '$12,500.00', downPayment: '$3,750.00', status: 'Delivered' },
    { id: 'P00008', vendor: 'Wood Corner', customer: 'Event Spaces', expectedArrival: 'Feb 8, 2025', total: '$7,435.33', downPayment: '$2,230.60', status: 'Pending' },
    { id: 'P00007', vendor: 'Ready Mat', customer: 'Retail Outlet', expectedArrival: 'Feb 10, 2025', total: '$1,405.88', downPayment: '$421.76', status: 'In Processing' },
    { id: 'P00006', vendor: 'Wood Corner', customer: 'Shopping Mall', expectedArrival: 'Feb 12, 2025', total: '$1,535.25', downPayment: '$460.58', status: 'Shipped' },
    { id: 'P00005', vendor: 'Deco Addict', customer: 'Luxury Brands', expectedArrival: 'Feb 15, 2025', total: '$9,956.70', downPayment: '$2,987.01', status: 'Delivered' },
    { id: 'P00004', vendor: 'Ready Mat', customer: 'Department Store', expectedArrival: 'Feb 18, 2025', total: '$16,747.45', downPayment: '$5,024.24', status: 'Pending' },
    { id: 'P00003', vendor: 'Azure Interior', customer: 'Boutique Store', expectedArrival: 'Feb 20, 2025', total: '$293.25', downPayment: '$87.98', status: 'In Processing' },
    { id: 'P00002', vendor: 'Gemini Furniture', customer: 'Home Center', expectedArrival: 'Feb 22, 2025', total: '$3,095.00', downPayment: '$928.50', status: 'Shipped' },
    { id: 'P00001', vendor: 'Wood Corner', customer: 'Trade Center', expectedArrival: 'Feb 25, 2025', total: '$3,480.48', downPayment: '$1,044.14', status: 'Delivered' }
  ]);

  const statusOptions = ['Pending', 'In Processing', 'Shipped', 'Delivered'];

  const uniqueVendors = [...new Set(orders.map(order => order.vendor))];
  const uniqueCustomers = [...new Set(orders.map(order => order.customer))];

  const setThisWeek = () => {
    const today = new Date();
    const startOfWeek = new Date(today);
    const endOfWeek = new Date(today);

    startOfWeek.setDate(today.getDate() - today.getDay());
    endOfWeek.setDate(today.getDate() + (6 - today.getDay()));

    setFilters(prev => ({
      ...prev,
      dateFrom: startOfWeek.toISOString().split('T')[0],
      dateTo: endOfWeek.toISOString().split('T')[0]
    }));
    setCurrentPage(1);
  };

  const setThisMonth = () => {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    setFilters(prev => ({
      ...prev,
      dateFrom: startOfMonth.toISOString().split('T')[0],
      dateTo: endOfMonth.toISOString().split('T')[0]
    }));
    setCurrentPage(1);
  };

  const clearFilters = () => {
    setFilters({
      status: '',
      vendor: '',
      customer: '',
      dateFrom: '',
      dateTo: '',
      minAmount: '',
      maxAmount: ''
    });
    setCurrentPage(1);
  };

  const setStatusFilter = (status: string) => {
    setFilters(prev => ({
      ...prev,
      status: filters.status === status ? '' : status
    }));
    setCurrentPage(1);
  };

  const getStatusCount = (status: string) => {
    return orders.filter(order => order.status === status).length;
  };

  const filteredOrders = orders.filter(order => {
    const orderTotal = parseFloat(order.total.replace(/[$,]/g, ''));
    const orderDate = new Date(order.expectedArrival);

    if (filters.status && order.status !== filters.status) return false;

    if (filters.vendor && order.vendor !== filters.vendor) return false;

    if (filters.customer && !order.customer.toLowerCase().includes(filters.customer.toLowerCase())) return false;

    if (filters.dateFrom && orderDate < new Date(filters.dateFrom)) return false;
    if (filters.dateTo && orderDate > new Date(filters.dateTo)) return false;

    if (filters.minAmount && orderTotal < parseFloat(filters.minAmount)) return false;
    if (filters.maxAmount && orderTotal > parseFloat(filters.maxAmount)) return false;

    return true;
  });

  // Pagination calculations
  const totalPages = Math.ceil(filteredOrders.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentOrders = filteredOrders.slice(startIndex, endIndex);
  const totalItems = filteredOrders.length;
  const startItem = totalItems === 0 ? 0 : startIndex + 1;
  const endItem = Math.min(endIndex, totalItems);

  const hasActiveFilters = Object.values(filters).some(value => value !== '');

  const toggleOrderSelection = (orderId: string) => {
    setSelectedOrders(prev =>
      prev.includes(orderId)
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    );
  };

  const updateOrderStatus = (orderId: string, newStatus: string) => {
    // In a real app, this would update the orders state
    setEditingStatus(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'In Processing':
        return 'bg-blue-100 text-blue-800';
      case 'Shipped':
        return 'bg-purple-100 text-purple-800';
      case 'Delivered':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const calculateRemaining = (total: string, downPayment: string) => {
    const totalAmount = parseFloat(total.replace(/[$,]/g, ''));
    const downAmount = parseFloat(downPayment.replace(/[$,]/g, ''));
    const remaining = totalAmount - downAmount;
    return `$${remaining.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
  };

  const moveOrder = (orderId: string, newStatus: string) => {
    // In a real app, this would update the orders state
  };

  const getOrdersByStatus = (status: string) => {
    return currentOrders.filter(order => order.status === status);
  };

  const handleDragStart = (e: React.DragEvent, orderId: string) => {
    e.dataTransfer.setData('text/plain', orderId);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, newStatus: string) => {
    e.preventDefault();
    const orderId = e.dataTransfer.getData('text/plain');
    moveOrder(orderId, newStatus);
  };

  const goToPage = (page: number) => {
    setCurrentPage(page);
    setSelectedOrders([]); // Clear selections when changing pages
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm">
      {/* Filter Header - Improved spacing */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 whitespace-nowrap cursor-pointer"
            >
              <i className="ri-filter-line w-4 h-4 flex items-center justify-center"></i>
              <span>Filters</span>
              {hasActiveFilters && (
                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                  {Object.values(filters).filter(v => v !== '').length}
                </span>
              )}
            </button>
            {hasActiveFilters && (
              <button
                onClick={clearFilters}
                className="text-sm text-blue-600 hover:text-blue-800 cursor-pointer whitespace-nowrap"
              >
                Clear all
              </button>
            )}
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-500">
              {startItem}-{endItem} of {totalItems} orders
            </div>
            <div className="flex items-center space-x-2">
              <button 
                onClick={goToPrevPage}
                disabled={currentPage === 1}
                className="p-2 hover:bg-gray-100 rounded-lg cursor-pointer transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <i className="ri-arrow-left-s-line w-5 h-5 flex items-center justify-center text-gray-600"></i>
              </button>
              <span className="text-sm text-gray-600 px-2">
                Page {currentPage} of {totalPages}
              </span>
              <button 
                onClick={goToNextPage}
                disabled={currentPage === totalPages}
                className="p-2 hover:bg-gray-100 rounded-lg cursor-pointer transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <i className="ri-arrow-right-s-line w-5 h-5 flex items-center justify-center text-gray-600"></i>
              </button>
            </div>
            <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded transition-all duration-200 cursor-pointer ${
                  viewMode === 'list' 
                    ? 'bg-white text-gray-900 shadow-sm' 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                title="List View"
              >
                <i className="ri-list-unordered w-4 h-4 flex items-center justify-center"></i>
              </button>
              <button
                onClick={() => setViewMode('card')}
                className={`p-2 rounded transition-all duration-200 cursor-pointer ${
                  viewMode === 'card' 
                    ? 'bg-white text-gray-900 shadow-sm' 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                title="Card View"
              >
                <i className="ri-layout-grid-line w-4 h-4 flex items-center justify-center"></i>
              </button>
              <button
                onClick={() => setViewMode('kanban')}
                className={`p-2 rounded transition-all duration-200 cursor-pointer ${
                  viewMode === 'kanban' 
                    ? 'bg-white text-gray-900 shadow-sm' 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                title="Kanban View"
              >
                <i className="ri-layout-column-line w-4 h-4 flex items-center justify-center"></i>
              </button>
            </div>
          </div>
        </div>

        {/* Quick Status Filter Cards - Better spacing */}
        <div className="flex items-center space-x-3 flex-wrap gap-y-2">
          <span className="text-sm font-medium text-gray-600 whitespace-nowrap">Quick filters:</span>
          {statusOptions.map(status => (
            <button
              key={status}
              onClick={() => setStatusFilter(status)}
              className={`inline-flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 cursor-pointer whitespace-nowrap ${
                filters.status === status
                  ? 'bg-blue-100 text-blue-700 border border-blue-300 shadow-sm'
                  : 'bg-gray-100 text-gray-600 border border-gray-200 hover:bg-gray-200'
              }`}
            >
              <span>{status}</span>
              <span
                className={`inline-flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-bold rounded-full ${
                  filters.status === status
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-500 text-white'
                }`}
              >
                {getStatusCount(status)}
              </span>
            </button>
          ))}
          <div className="h-6 border-l border-gray-300 mx-3"></div>
          <span className="text-sm font-medium text-gray-600 whitespace-nowrap">Time:</span>
          <button
            onClick={setThisWeek}
            className="px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 whitespace-nowrap cursor-pointer"
          >
            This Week
          </button>
          <button
            onClick={setThisMonth}
            className="px-3 py-2 text-sm font-medium text-green-600 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 whitespace-nowrap cursor-pointer"
          >
            This Month
          </button>
        </div>
      </div>

      {/* Advanced Filters Panel - Better spacing */}
      {showFilters && (
        <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <div className="relative">
                <select
                  value={filters.status}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, status: e.target.value }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-8"
                >
                  <option value="">All statuses</option>
                  {statusOptions.map(status => (
                    <option key={status} value={status}>
                      {status}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Vendor Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Vendor</label>
              <div className="relative">
                <select
                  value={filters.vendor}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, vendor: e.target.value }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-8"
                >
                  <option value="">All vendors</option>
                  {uniqueVendors.map(vendor => (
                    <option key={vendor} value={vendor}>
                      {vendor}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Customer Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Customer</label>
              <input
                type="text"
                placeholder="Search customer..."
                value={filters.customer}
                onChange={(e) => {
                  setFilters(prev => ({ ...prev, customer: e.target.value }));
                  setCurrentPage(1);
                }}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Amount Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Amount Range</label>
              <div className="flex space-x-2">
                <input
                  type="number"
                  placeholder="Min"
                  value={filters.minAmount}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, minAmount: e.target.value }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <input
                  type="number"
                  placeholder="Max"
                  value={filters.maxAmount}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, maxAmount: e.target.value }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Date Range */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">Expected Arrival Date</label>
              <div className="flex space-x-2">
                <input
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, dateFrom: e.target.value }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <span className="flex items-center text-gray-500 text-sm px-2">to</span>
                <input
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => {
                    setFilters(prev => ({ ...prev, dateTo: e.target.value }));
                    setCurrentPage(1);
                  }}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Content Area - Better spacing for all views */}
      {viewMode === 'kanban' ? (
        <div className="p-6">
          <div className="grid grid-cols-4 gap-4">
            {statusOptions.map(status => {
              const statusOrders = getOrdersByStatus(status);
              const statusConfig = {
                'Pending': { 
                  color: 'bg-yellow-500', 
                  lightColor: 'bg-yellow-50',
                  borderColor: 'border-yellow-200',
                  textColor: 'text-yellow-700'
                },
                'In Processing': { 
                  color: 'bg-blue-500', 
                  lightColor: 'bg-blue-50',
                  borderColor: 'border-blue-200',
                  textColor: 'text-blue-700'
                },
                'Shipped': { 
                  color: 'bg-purple-500', 
                  lightColor: 'bg-purple-50',
                  borderColor: 'border-purple-200',
                  textColor: 'text-purple-700'
                },
                'Delivered': { 
                  color: 'bg-green-500', 
                  lightColor: 'bg-green-50',
                  borderColor: 'border-green-200',
                  textColor: 'text-green-700'
                }
              };
              const config = statusConfig[status];
              
              return (
                <div
                  key={status}
                  className="bg-gray-50 rounded-lg"
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, status)}
                >
                  {/* Column Header */}
                  <div className="p-4 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className={`w-3 h-3 rounded-full ${config.color}`}></div>
                        <h3 className="text-sm font-semibold text-gray-900">{status}</h3>
                      </div>
                      <span className="text-xs text-gray-500 bg-white px-2 py-1 rounded-full border">
                        {statusOrders.length}
                      </span>
                    </div>
                  </div>
                  
                  {/* Cards Container */}
                  <div className="p-3 space-y-3 min-h-[500px] max-h-[600px] overflow-y-auto">
                    {statusOrders.map(order => (
                      <div
                        key={order.id}
                        draggable
                        onDragStart={(e) => handleDragStart(e, order.id)}
                        className={`bg-white rounded-xl border border-gray-200 p-4 cursor-move hover:shadow-xl transition-all duration-300 hover:scale-105 group ${
                          selectedOrders.includes(order.id) ? 'border-blue-400 shadow-lg ring-2 ring-blue-100' : 'hover:border-gray-300'
                        }`}
                      >
                        {/* Card Header */}
                        <div className="flex items-center justify-between mb-4">
                          <span className="text-base font-bold text-blue-600 hover:text-blue-800 transition-colors">
                            {order.id}
                          </span>
                          <div className="flex items-center space-x-3">
                            <button className="text-gray-300 hover:text-yellow-500 transition-all duration-200 hover:scale-110">
                              <i className="ri-star-line w-5 h-5 flex items-center justify-center"></i>
                            </button>
                            <input
                              type="checkbox"
                              className="rounded border-gray-300 w-5 h-5 text-blue-600 focus:ring-blue-500 cursor-pointer"
                              checked={selectedOrders.includes(order.id)}
                              onChange={() => toggleOrderSelection(order.id)}
                            />
                          </div>
                        </div>
                        
                        {/* Vendor & Customer */}
                        <div className="space-y-3 mb-4">
                          <div className="bg-gray-50 rounded-lg p-3">
                            <div className="text-xs font-medium text-gray-500 mb-1 uppercase tracking-wide">Vendor</div>
                            <div className="text-sm font-semibold text-gray-900 truncate" title={order.vendor}>
                              {order.vendor}
                            </div>
                          </div>
                          <div className="bg-gray-50 rounded-lg p-3">
                            <div className="text-xs font-medium text-gray-500 mb-1 uppercase tracking-wide">Customer</div>
                            <div className="text-sm font-semibold text-gray-900 truncate" title={order.customer}>
                              {order.customer}
                            </div>
                          </div>
                        </div>
                        
                        {/* Expected Arrival */}
                        <div className="mb-4">
                          <div className="flex items-center space-x-2 bg-blue-50 rounded-lg p-3">
                            <i className="ri-calendar-2-line w-4 h-4 flex items-center justify-center text-blue-600"></i>
                            <div>
                              <div className="text-xs font-medium text-blue-600 mb-1 uppercase tracking-wide">Expected Arrival</div>
                              <div className="text-sm font-semibold text-blue-800">{order.expectedArrival}</div>
                            </div>
                          </div>
                        </div>
                        
                        {/* Financial Info */}
                        <div className="border-t border-gray-100 pt-4">
                          <div className="grid grid-cols-2 gap-3 mb-3">
                            <div className="text-center bg-green-50 rounded-lg p-3">
                              <div className="text-xs font-medium text-green-600 mb-1 uppercase tracking-wide">Total</div>
                              <div className="text-lg font-bold text-green-800">{order.total}</div>
                            </div>
                            <div className="text-center bg-orange-50 rounded-lg p-3">
                              <div className="text-xs font-medium text-orange-600 mb-1 uppercase tracking-wide">Remaining</div>
                              <div className="text-lg font-bold text-orange-800">
                                {calculateRemaining(order.total, order.downPayment)}
                              </div>
                            </div>
                          </div>
                          <div className="text-center bg-purple-50 rounded-lg p-2">
                            <div className="text-xs font-medium text-purple-600 uppercase tracking-wide">
                              Down Payment: <span className="font-bold text-purple-800">{order.downPayment}</span>
                            </div>
                          </div>
                        </div>
                        
                        {/* Hover Effect Overlay */}
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none"></div>
                      </div>
                    ))}
                    
                    {/* Empty State */}
                    {statusOrders.length === 0 && (
                      <div className="flex flex-col items-center justify-center py-12 text-gray-400">
                        <div className="w-12 h-12 rounded-full border-2 border-dashed border-gray-300 flex items-center justify-center mb-3 bg-gray-50">
                          <i className="ri-drag-drop-line w-6 h-6 flex items-center justify-center"></i>
                        </div>
                        <div className="text-sm font-medium text-center text-gray-500">
                          Drop orders here
                        </div>
                        <div className="text-xs text-center text-gray-400 mt-1">
                          Drag cards from other columns
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ) : viewMode === 'list' ? (
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 w-4 h-4 cursor-pointer"
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedOrders(currentOrders.map(order => order.id));
                      } else {
                        setSelectedOrders([]);
                      }
                    }}
                  />
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Reference
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Vendor
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Expected Arrival
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  <div className="space-y-1">
                    <div>Total Amount</div>
                    <div className="text-gray-400 font-normal">Down Payment</div>
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Remaining
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  <i className="ri-more-2-line w-4 h-4 flex items-center justify-center mx-auto"></i>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-100">
              {currentOrders.map((order, index) => (
                <tr key={order.id} className={`hover:bg-blue-25 transition-colors duration-150 ${selectedOrders.includes(order.id) ? 'bg-blue-50 border-l-4 border-blue-400' : ''}`}>
                  <td className="px-6 py-5 whitespace-nowrap">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300 w-4 h-4 text-blue-600 focus:ring-blue-500 cursor-pointer"
                      checked={selectedOrders.includes(order.id)}
                      onChange={() => toggleOrderSelection(order.id)}
                    />
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="text-sm font-semibold text-blue-600 hover:text-blue-800 cursor-pointer">
                        {order.id}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {order.vendor}
                    </div>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {order.customer}
                    </div>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <i className="ri-calendar-line w-4 h-4 flex items-center justify-center text-gray-400"></i>
                      <span className="text-sm text-gray-700">
                        {order.expectedArrival}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap">
                    <div className="space-y-2">
                      <div className="text-sm font-semibold text-gray-900">{order.total}</div>
                      <div className="text-xs text-gray-500 bg-gray-50 px-3 py-1 rounded-lg">
                        {order.downPayment}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap">
                    <div className="text-sm font-semibold text-orange-600">
                      {calculateRemaining(order.total, order.downPayment)}
                    </div>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap relative">
                    <button
                      onClick={() => setEditingStatus(editingStatus === order.id ? null : order.id)}
                      className={`inline-flex items-center px-3 py-2 text-xs font-semibold rounded-full whitespace-nowrap cursor-pointer hover:shadow-md transition-all duration-200 ${getStatusColor(order.status)} border`}
                    >
                      <div className={`w-2 h-2 rounded-full mr-2 ${
                        order.status === 'Pending' ? 'bg-yellow-500' :
                        order.status === 'In Processing' ? 'bg-blue-500' :
                        order.status === 'Shipped' ? 'bg-purple-500' :
                        'bg-green-500'
                      }`}></div>
                      {order.status}
                      <i className="ri-arrow-down-s-line ml-2 w-3 h-3 flex items-center justify-center"></i>
                    </button>
                    {editingStatus === order.id && (
                      <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-xl z-10 min-w-[160px] py-2">
                        {statusOptions.map((status) => (
                          <button
                            key={status}
                            onClick={() => updateOrderStatus(order.id, status)}
                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 whitespace-nowrap"
                          >
                            <div className="flex items-center">
                              <div className={`w-2 h-2 rounded-full mr-3 ${
                                status === 'Pending' ? 'bg-yellow-500' :
                                status === 'In Processing' ? 'bg-blue-500' :
                                status === 'Shipped' ? 'bg-purple-500' :
                                'bg-green-500'
                              }`}></div>
                              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(status)}`}>
                                {status}
                              </span>
                            </div>
                          </button>
                        ))}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap text-center">
                    <button className="text-gray-400 hover:text-yellow-500 transition-colors duration-150 cursor-pointer">
                      <i className="ri-star-line w-4 h-4 flex items-center justify-center"></i>
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {currentOrders.map((order) => (
              <div
                key={order.id}
                className={`relative bg-white border border-gray-200 rounded-xl p-5 hover:shadow-lg transition-all duration-200 cursor-pointer ${
                  selectedOrders.includes(order.id) ? 'border-blue-400 bg-blue-50' : ''
                }`}
              >
                <div className="absolute top-4 right-4">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 w-4 h-4 text-blue-600 focus:ring-blue-500 cursor-pointer"
                    checked={selectedOrders.includes(order.id)}
                    onChange={() => toggleOrderSelection(order.id)}
                  />
                </div>
                
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm font-semibold text-blue-600 hover:text-blue-800">
                      {order.id}
                    </span>
                    <button className="text-gray-400 hover:text-yellow-500 transition-colors duration-150">
                      <i className="ri-star-line w-4 h-4 flex items-center justify-center"></i>
                    </button>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <div className="text-xs text-gray-500 mb-1">Vendor</div>
                      <div className="text-sm font-medium text-gray-900">{order.vendor}</div>
                    </div>
                    
                    <div>
                      <div className="text-xs text-gray-500 mb-1">Customer</div>
                      <div className="text-sm text-gray-900">{order.customer}</div>
                    </div>
                    
                    <div>
                      <div className="text-xs text-gray-500 mb-1">Expected Arrival</div>
                      <div className="flex items-center space-x-2">
                        <i className="ri-calendar-line w-4 h-4 flex items-center justify-center text-gray-400"></i>
                        <span className="text-sm text-gray-700">{order.expectedArrival}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="border-t border-gray-100 pt-4">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <div className="text-xs text-gray-500 mb-1">Total Amount</div>
                      <div className="text-sm font-semibold text-gray-900">{order.total}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-xs text-gray-500 mb-1">Remaining</div>
                      <div className="text-sm font-semibold text-orange-600">
                        {calculateRemaining(order.total, order.downPayment)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    <div className="text-xs text-gray-500 mb-1">Down Payment</div>
                    <div className="text-xs text-gray-600 bg-gray-50 px-3 py-2 rounded-lg">
                      {order.downPayment}
                    </div>
                  </div>
                  
                  <div className="relative">
                    <button
                      onClick={() => setEditingStatus(editingStatus === order.id ? null : order.id)}
                      className={`w-full inline-flex items-center justify-center px-3 py-2 text-xs font-semibold rounded-lg transition-all duration-200 ${getStatusColor(order.status)} border hover:shadow-md`}
                    >
                      <div className={`w-2 h-2 rounded-full mr-2 ${
                        order.status === 'Pending' ? 'bg-yellow-500' :
                        order.status === 'In Processing' ? 'bg-blue-500' :
                        order.status === 'Shipped' ? 'bg-purple-500' :
                        'bg-green-500'
                      }`}></div>
                      {order.status}
                      <i className="ri-arrow-down-s-line ml-2 w-3 h-3 flex items-center justify-center"></i>
                    </button>
                    {editingStatus === order.id && (
                      <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-xl z-10 py-2">
                        {statusOptions.map((status) => (
                          <button
                            key={status}
                            onClick={() => updateOrderStatus(order.id, status)}
                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                          >
                            <div className="flex items-center">
                              <div className={`w-2 h-2 rounded-full mr-3 ${
                                status === 'Pending' ? 'bg-yellow-500' :
                                status === 'In Processing' ? 'bg-blue-500' :
                                status === 'Shipped' ? 'bg-purple-500' :
                                'bg-green-500'
                              }`}></div>
                              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(status)}`}>
                                {status}
                              </span>
                            </div>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="px-6 py-4 border-t border-gray-200 bg-white">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {startItem} to {endItem} of {totalItems} results
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={goToPrevPage}
                disabled={currentPage === 1}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer whitespace-nowrap"
              >
                Previous
              </button>
              
              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }
                  
                  return (
                    <button
                      key={pageNum}
                      onClick={() => goToPage(pageNum)}
                      className={`px-3 py-2 text-sm font-medium rounded-lg cursor-pointer whitespace-nowrap ${
                        currentPage === pageNum
                          ? 'bg-blue-600 text-white border border-blue-600'
                          : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>
              
              <button
                onClick={goToNextPage}
                disabled={currentPage === totalPages}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer whitespace-nowrap"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
      
      {filteredOrders.length === 0 && (
        <div className="text-center py-16">
          <i className="ri-file-list-3-line w-16 h-16 flex items-center justify-center mx-auto text-gray-300 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
          <p className="text-sm text-gray-500">Try adjusting your filters to see more results.</p>
        </div>
      )}
      
      {editingStatus && (
        <div className="fixed inset-0 z-5" onClick={() => setEditingStatus(null)} />
      )}
    </div>
  );
}
