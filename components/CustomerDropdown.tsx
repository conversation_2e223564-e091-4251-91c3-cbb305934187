
'use client';

import { useState, useEffect, useRef } from 'react';

interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  company?: string;
  address?: string;
  status: 'Active' | 'Inactive';
  totalOrders: number;
  totalAmount: number;
  lastOrderDate?: string;
}

interface CustomerDropdownProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
}

export default function CustomerDropdown({ value, onChange, placeholder = "Select customer", required = false }: CustomerDropdownProps) {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Load customers from both sales data and customer profiles
    const loadCustomers = () => {
      if (typeof window !== 'undefined') {
        // Load existing customers from sales data
        const salesStore = require('@/lib/salesStore').default;
        const store = salesStore.getInstance();
        const sales = store.getSales();
        
        // Get unique customers from sales
        const salesCustomers = Array.from(new Set(sales.map((sale: any) => sale.customer)))
          .map(customerName => ({
            id: `sales-${customerName}`,
            name: customerName as string,
            status: 'Active' as const,
            totalOrders: sales.filter((sale: any) => sale.customer === customerName).length,
            totalAmount: sales
              .filter((sale: any) => sale.customer === customerName)
              .reduce((sum: number, sale: any) => sum + parseFloat(sale.total.replace(/[$,]/g, '')), 0)
          }));

        // Load customer profiles (customers added via modal)
        const customerProfiles = JSON.parse(localStorage.getItem('customerProfiles') || '{}');
        const profileCustomers = Object.values(customerProfiles).map((profile: any) => ({
          id: `profile-${profile.name}`,
          name: profile.name,
          email: profile.email,
          phone: profile.phone,
          company: profile.company,
          address: profile.address,
          status: profile.status || 'Inactive',
          totalOrders: 0,
          totalAmount: 0
        }));

        // Merge and deduplicate customers
        const allCustomers = [...salesCustomers];
        profileCustomers.forEach(profileCustomer => {
          const existsInSales = salesCustomers.find(sc => sc.name === profileCustomer.name);
          if (!existsInSales) {
            allCustomers.push(profileCustomer);
          }
        });

        setCustomers(allCustomers);
      }
    };

    loadCustomers();
  }, []);

  useEffect(() => {
    // Filter customers based on search term
    const filtered = customers.filter(customer =>
      customer.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    // Show max 3 options as requested
    setFilteredCustomers(filtered.slice(0, 3));
  }, [customers, searchTerm]);

  useEffect(() => {
    // Close dropdown when clicking outside
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    setSearchTerm(inputValue);
    onChange(inputValue);
    
    if (inputValue.length > 0 && !isOpen) {
      setIsOpen(true);
    }
  };

  const handleCustomerSelect = (customerName: string) => {
    onChange(customerName);
    setSearchTerm(customerName);
    setIsOpen(false);
  };

  const handleInputFocus = () => {
    setIsOpen(true);
  };

  const handleInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsOpen(false);
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      setIsOpen(true);
    }
  };

  return (
    <div ref={dropdownRef} className="relative">
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onKeyDown={handleInputKeyDown}
          placeholder={placeholder}
          required={required}
          className="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
        />
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 cursor-pointer"
        >
          <i className={`ri-arrow-${isOpen ? 'up' : 'down'}-s-line w-4 h-4 flex items-center justify-center`}></i>
        </button>
      </div>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-48 overflow-y-auto">
          {filteredCustomers.length > 0 ? (
            <div className="py-1">
              {filteredCustomers.map((customer) => (
                <button
                  key={customer.id}
                  type="button"
                  onClick={() => handleCustomerSelect(customer.name)}
                  className="w-full px-4 py-3 text-left hover:bg-gray-50 cursor-pointer flex items-center justify-between"
                >
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">{customer.name}</span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        customer.status === 'Active' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {customer.status}
                      </span>
                    </div>
                    {customer.company && (
                      <div className="text-sm text-gray-500 mt-1">{customer.company}</div>
                    )}
                    <div className="text-xs text-gray-400 mt-1">
                      {customer.totalOrders} orders • ${customer.totalAmount.toLocaleString()}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          ) : searchTerm.length > 0 ? (
            <div className="px-4 py-3 text-gray-500 text-sm">
              No customers found matching "{searchTerm}"
            </div>
          ) : customers.length === 0 ? (
            <div className="px-4 py-3 text-gray-500 text-sm">
              No customers available. Add customers first.
            </div>
          ) : (
            <div className="px-4 py-3 text-gray-500 text-sm">
              Start typing to search customers...
            </div>
          )}
        </div>
      )}
    </div>
  );
}
