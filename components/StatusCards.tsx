
'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function StatusCards() {
  const [activeTab, setActiveTab] = useState('All');

  const tabs = ['All', 'My'];

  const stats = [
    { 
      value: '10', 
      label: 'Pending Orders', 
      bgColor: 'bg-blue-50', 
      textColor: 'text-blue-800',
      iconColor: 'text-blue-600',
      icon: 'ri-time-line',
      borderColor: 'border-blue-200'
    },
    { 
      value: '5', 
      label: 'In Progress', 
      bgColor: 'bg-orange-50', 
      textColor: 'text-orange-800',
      iconColor: 'text-orange-600',
      icon: 'ri-loader-4-line',
      borderColor: 'border-orange-200'
    },
    { 
      value: '6%', 
      label: 'Delayed Orders', 
      bgColor: 'bg-red-50', 
      textColor: 'text-red-800',
      iconColor: 'text-red-600',
      icon: 'ri-alarm-warning-line',
      borderColor: 'border-red-200'
    },
    { 
      value: '1.75', 
      label: 'Delivered Orders', 
      bgColor: 'bg-green-50', 
      textColor: 'text-green-800',
      iconColor: 'text-green-600',
      icon: 'ri-truck-line',
      borderColor: 'border-green-200'
    },
    { 
      value: '$127K', 
      label: 'Down Payment Orders', 
      bgColor: 'bg-purple-50', 
      textColor: 'text-purple-800',
      iconColor: 'text-purple-600',
      icon: 'ri-wallet-3-line',
      borderColor: 'border-purple-200'
    }
  ];

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Link href="/sales/new">
            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium whitespace-nowrap cursor-pointer hover:bg-blue-700 transition-colors">
              Create Sales Order
            </button>
          </Link>
          <button className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg text-sm font-medium whitespace-nowrap cursor-pointer hover:bg-gray-50 transition-colors">
            Upload
          </button>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700 font-medium">Requests for Quotation</span>
            <i className="ri-arrow-down-s-line w-5 h-5 flex items-center justify-center text-gray-600"></i>
          </div>
        </div>
        
        <div className="flex items-center space-x-6">
          <div className="relative">
            <input 
              type="text" 
              placeholder="Search..."
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm w-64 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <i className="ri-search-line w-4 h-4 flex items-center justify-center absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          </div>
          <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
            <button className="p-2 hover:bg-white rounded-lg cursor-pointer transition-all duration-200">
              <i className="ri-list-unordered w-5 h-5 flex items-center justify-center text-gray-600"></i>
            </button>
            <button className="p-2 hover:bg-white rounded-lg cursor-pointer transition-all duration-200">
              <i className="ri-layout-grid-line w-5 h-5 flex items-center justify-center text-gray-600"></i>
            </button>
            <button className="p-2 hover:bg-white rounded-lg cursor-pointer transition-all duration-200">
              <i className="ri-bar-chart-line w-5 h-5 flex items-center justify-center text-gray-600"></i>
            </button>
            <button className="p-2 hover:bg-white rounded-lg cursor-pointer transition-all duration-200">
              <i className="ri-upload-line w-5 h-5 flex items-center justify-center text-gray-600"></i>
            </button>
            <button className="p-2 hover:bg-white rounded-lg cursor-pointer transition-all duration-200">
              <i className="ri-calendar-line w-5 h-5 flex items-center justify-center text-gray-600"></i>
            </button>
            <button className="p-2 hover:bg-white rounded-lg cursor-pointer transition-all duration-200">
              <i className="ri-settings-line w-5 h-5 flex items-center justify-center text-gray-600"></i>
            </button>
          </div>
        </div>
      </div>

      <div className="flex items-center space-x-6 mb-6">
        {tabs.map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 text-sm font-medium rounded-full cursor-pointer whitespace-nowrap transition-all duration-200 ${
              activeTab === tab 
                ? 'bg-blue-100 text-blue-800 shadow-sm' 
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      <div className="grid grid-cols-5 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className={`relative p-6 rounded-xl border ${stat.bgColor} ${stat.borderColor} hover:shadow-lg transition-all duration-200 cursor-pointer group`}>
            <div className="flex items-start justify-between mb-4">
              <div className={`p-3 rounded-xl bg-white/80 ${stat.iconColor} shadow-sm`}>
                <i className={`${stat.icon} w-6 h-6 flex items-center justify-center`}></i>
              </div>
              <div className="text-right">
                <div className={`text-3xl font-bold mb-1 ${stat.textColor}`}>
                  {stat.value}
                </div>
                {activeTab === 'My' && index < 2 && (
                  <div className={`text-sm opacity-60 ${stat.textColor}`}>
                    {index === 0 ? '9' : '5'}
                  </div>
                )}
              </div>
            </div>
            <div className={`text-sm font-medium ${stat.textColor} opacity-90`}>
              {stat.label}
            </div>
            <div className="absolute inset-0 bg-gradient-to-r from-transparent to-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-xl"></div>
          </div>
        ))}
      </div>
    </div>
  );
}
