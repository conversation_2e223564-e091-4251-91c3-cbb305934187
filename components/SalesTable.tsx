
'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import SalesStore, { Sale } from '@/lib/salesStore';
import { useTranslation } from '@/hooks/useTranslation';

export default function SalesTable() {
  const { t } = useTranslation();
  const [sales, setSales] = useState<Sale[]>([]);
  const [selectedSales, setSelectedSales] = useState<string[]>([]);
  const [editingStatus, setEditingStatus] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'list' | 'card' | 'kanban'>('list');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [filters, setFilters] = useState({
    status: '',
    vendor: '',
    customer: '',
    dateFrom: '',
    dateTo: '',
    minAmount: '',
    maxAmount: ''
  });

  // Export functionality state
  const [showExportModal, setShowExportModal] = useState(false);
  const [exportFormat, setExportFormat] = useState<'excel' | 'html'>('html');
  const [selectedExportColumns, setSelectedExportColumns] = useState(['id', 'vendor', 'customer', 'salesDate', 'total', 'status']);

  // Fixed status options - using English values for internal logic
  const statusOptions = ['Completed', 'Processing', 'Shipped', 'Delivered'];

  // Initialize sales data and subscribe to changes
  useEffect(() => {
    const salesStore = SalesStore.getInstance();
    setSales(salesStore.getSales());
    
    const unsubscribe = salesStore.subscribe(() => {
      setSales(salesStore.getSales());
    });

    return unsubscribe;
  }, []);

  const uniqueVendors = [...new Set(sales.map(sale => sale.vendor))];
  const uniqueCustomers = [...new Set(sales.map(sale => sale.customer))];

  const setThisWeek = () => {
    const today = new Date();
    const startOfWeek = new Date(today);
    const endOfWeek = new Date(today);

    startOfWeek.setDate(today.getDate() - today.getDay());
    endOfWeek.setDate(today.getDate() + (6 - today.getDay()));

    setFilters(prev => ({
      ...prev,
      dateFrom: startOfWeek.toISOString().split('T')[0],
      dateTo: endOfWeek.toISOString().split('T')[0]
    }));
    setCurrentPage(1);
  };

  const setThisMonth = () => {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    setFilters(prev => ({
      ...prev,
      dateFrom: startOfMonth.toISOString().split('T')[0],
      dateTo: endOfMonth.toISOString().split('T')[0]
    }));
    setCurrentPage(1);
  };

  const clearFilters = () => {
    setFilters({
      status: '',
      vendor: '',
      customer: '',
      dateFrom: '',
      dateTo: '',
      minAmount: '',
      maxAmount: ''
    });
    setCurrentPage(1);
  };

  const setStatusFilter = (status: string) => {
    setFilters(prev => ({
      ...prev,
      status: filters.status === status ? '' : status
    }));
    setCurrentPage(1);
  };

  const getStatusCount = (status: string) => {
    return sales.filter(sale => sale.status === status).length;
  };

  // Function to translate status for display
  const getTranslatedStatus = (status: string) => {
    switch (status) {
      case 'Completed':
        return t('sales.statuses.completed');
      case 'Processing':
        return t('sales.statuses.processing');
      case 'Shipped':
        return t('sales.statuses.shipped');
      case 'Delivered':
        return t('sales.statuses.delivered');
      case 'Pending':
        return t('sales.statuses.pending');
      case 'Confirmed':
        return t('sales.statuses.confirmed');
      case 'Cancelled':
        return t('sales.statuses.cancelled');
      default:
        return status;
    }
  };

  const filteredSales = sales.filter(sale => {
    const saleTotal = parseFloat(sale.total.replace(/[$,]/g, ''));
    const saleDate = new Date(sale.salesDate);

    if (filters.status && sale.status !== filters.status) return false;
    if (filters.vendor && sale.vendor !== filters.vendor) return false;
    if (filters.customer && !sale.customer.toLowerCase().includes(filters.customer.toLowerCase())) return false;
    if (filters.dateFrom && saleDate < new Date(filters.dateFrom)) return false;
    if (filters.dateTo && saleDate > new Date(filters.dateTo)) return false;
    if (filters.minAmount && saleTotal < parseFloat(filters.minAmount)) return false;
    if (filters.maxAmount && saleTotal > parseFloat(filters.maxAmount)) return false;

    return true;
  });

  // Pagination calculations
  const totalPages = Math.ceil(filteredSales.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentSales = filteredSales.slice(startIndex, endIndex);
  const totalItems = filteredSales.length;
  const startItem = totalItems === 0 ? 0 : startIndex + 1;
  const endItem = Math.min(endIndex, totalItems);

  const hasActiveFilters = Object.values(filters).some(value => value !== '');

  const toggleSaleSelection = (saleId: string) => {
    setSelectedSales(prev =>
      prev.includes(saleId)
        ? prev.filter(id => id !== saleId)
        : [...prev, saleId]
    );
  };

  const updateSaleStatus = (saleId: string, newStatus: string) => {
    const salesStore = SalesStore.getInstance();
    salesStore.updateSaleStatus(saleId, newStatus);
    setEditingStatus(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-100 text-green-800';
      case 'Processing':
        return 'bg-blue-100 text-blue-800';
      case 'Shipped':
        return 'bg-purple-100 text-purple-800';
      case 'Delivered':
        return 'bg-gray-100 text-gray-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'Confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const calculateProfit = (total: string, commission: string) => {
    const totalAmount = parseFloat(total.replace(/[$,]/g, ''));
    const commissionAmount = parseFloat(commission.replace(/[$,]/g, ''));
    const profit = totalAmount - commissionAmount;
    return `$${profit.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
  };

  const moveSale = (saleId: string, newStatus: string) => {
    const salesStore = SalesStore.getInstance();
    salesStore.updateSaleStatus(saleId, newStatus);
  };

  const getSalesByStatus = (status: string) => {
    return currentSales.filter(sale => sale.status === status);
  };

  const handleDragStart = (e: React.DragEvent, saleId: string) => {
    e.dataTransfer.setData('text/plain', saleId);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, newStatus: string) => {
    e.preventDefault();
    const saleId = e.dataTransfer.getData('text/plain');
    moveSale(saleId, newStatus);
  };

  const goToPage = (page: number) => {
    setCurrentPage(page);
    setSelectedSales([]); // Clear selections when changing pages
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  };

  // Export helper functions
  const getAvailableExportColumns = () => {
    return [
      { id: 'id', name: t('sales.saleId') },
      { id: 'vendor', name: t('sales.vendor') },
      { id: 'customer', name: t('sales.customer') },
      { id: 'salesDate', name: t('common.date') },
      { id: 'total', name: t('common.total') },
      { id: 'profit', name: 'Profit' },
      { id: 'status', name: t('common.status') }
    ];
  };

  const generateSalesExport = () => {
    const availableColumns = getAvailableExportColumns();

    return filteredSales.map(sale => {
      const exportData: any = {};

      selectedExportColumns.forEach(columnId => {
        const column = availableColumns.find(col => col.id === columnId);
        if (!column) return;

        switch (columnId) {
          case 'id':
            exportData.id = sale.id;
            break;
          case 'vendor':
            exportData.vendor = sale.vendor;
            break;
          case 'customer':
            exportData.customer = sale.customer;
            break;
          case 'salesDate':
            exportData.salesDate = sale.salesDate;
            break;
          case 'total':
            exportData.total = sale.total;
            break;
          case 'profit':
            exportData.profit = calculateProfit(sale.total, sale.commission);
            break;
          case 'status':
            exportData.status = sale.status;
            break;
        }
      });

      return exportData;
    });
  };

  const downloadSalesHTML = (data: any) => {
    let htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${t('sales.title')} Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .report-container { max-width: 1200px; margin: 0 auto; }
        .report { background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .summary { padding: 20px 30px; background: #f8f9fa; border-bottom: 1px solid #e9ecef; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
        .summary-item { text-align: center; }
        .summary-value { font-size: 24px; font-weight: bold; color: #3b82f6; }
        .summary-label { font-size: 14px; color: #6b7280; margin-top: 5px; }
        .table-section { padding: 30px; }
        .sales-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        .sales-table th { background: #f8f9fa; padding: 15px 8px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; }
        .sales-table td { padding: 15px 8px; border-bottom: 1px solid #e9ecee; color: #6c757d; }
        .status-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .status-completed { background: #d1fae5; color: #065f46; }
        .status-processing { background: #dbeafe; color: #1e40af; }
        .status-shipped { background: #e0e7ff; color: #5b21b6; }
        .status-delivered { background: #f3f4f6; color: #374151; }
        .total-amount { font-weight: bold; color: #059669; }
        .profit-amount { font-weight: bold; color: #7c3aed; }
    </style>
</head>
<body>
    <div class="report-container">
        <div class="report">
            <div class="header">
                <h1>${t('sales.title')} Report</h1>
                <p>Generated on ${new Date().toLocaleDateString()}</p>
            </div>
            
            <div class="summary">
                <div class="summary-grid">
                    <div class="summary-item">
                        <div class="summary-value">${data.length}</div>
                        <div class="summary-label">${t('sales.title')}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-value">$${data.reduce((sum: number, sale: any) => sum + parseFloat(sale.total?.replace(/[$,]/g, '') || '0'), 0).toLocaleString()}</div>
                        <div class="summary-label">Total Revenue</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-value">$${data.reduce((sum: number, sale: any) => {
                          const total = parseFloat(sale.total?.replace(/[$,]/g, '') || '0');
                          const commission = parseFloat((data.find((s: any) => s.id === sale.id)?.commission || '$0').replace(/[$,]/g, '') || '0');
                          return sum + (total - commission);
                        }, 0).toLocaleString()}</div>
                        <div class="summary-label">Total Profit</div>
                    </div>
                </div>
            </div>
            
            <div class="table-section">
                <table class="sales-table">
                    <thead>
                        <tr>`;
    
    selectedExportColumns.forEach(columnId => {
      const column = getAvailableExportColumns().find(col => col.id === columnId);
      if (column) {
        htmlContent += `<th>${column.name}</th>`;
      }
    });
    
    htmlContent += `
                        </tr>
                    </thead>
                    <tbody>`;
    
    data.forEach((sale: any) => {
      htmlContent += `<tr>`;
      
      selectedExportColumns.forEach(columnId => {
        const column = getAvailableExportColumns().find(col => col.id === columnId);
        if (!column) return;
        
        switch (columnId) {
          case 'id':
            htmlContent += `<td><strong>${sale.id}</strong></td>`;
            break;
          case 'vendor':
            htmlContent += `<td>${sale.vendor}</td>`;
            break;
          case 'customer':
            htmlContent += `<td>${sale.customer}</td>`;
            break;
          case 'salesDate':
            htmlContent += `<td>${sale.salesDate}</td>`;
            break;
          case 'total':
            htmlContent += `<td class="total-amount">${sale.total}</td>`;
            break;
          case 'profit':
            htmlContent += `<td class="profit-amount">${sale.profit}</td>`;
            break;
          case 'status':
            const statusClass = sale.status.toLowerCase().replace(' ', '-');
            htmlContent += `<td><span class="status-badge status-${statusClass}">${sale.status}</span></td>`;
            break;
        }
      });
      
      htmlContent += `</tr>`;
    });
    
    htmlContent += `
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>`;

    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sales-report-${new Date().toISOString().split('T')[0]}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const downloadSalesExcel = (data: any) => {
    let htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #000; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .header-info { margin-bottom: 20px; }
        .summary-section { margin-top: 20px; background-color: #f9f9f9; padding: 10px; }
    </style>
</head>
<body>
    <div class="header-info">
        <h2>${t('sales.title')} Report Export</h2>
        <p><strong>Generated:</strong> ${new Date().toLocaleDateString()}</p>
        <p><strong>Total Records:</strong> ${data.length}</p>
    </div>
    
    <table>
        <thead>
            <tr>`;
    
    selectedExportColumns.forEach(columnId => {
      const column = getAvailableExportColumns().find(col => col.id === columnId);
      if (column) {
        htmlContent += `<th>${column.name}</th>`;
      }
    });
    
    htmlContent += `
            </tr>
        </thead>
        <tbody>`;
    
    data.forEach((sale: any) => {
      htmlContent += `<tr>`;
      
      selectedExportColumns.forEach(columnId => {
        switch (columnId) {
          case 'id':
            htmlContent += `<td>${sale.id}</td>`;
            break;
          case 'vendor':
            htmlContent += `<td>${sale.vendor}</td>`;
            break;
          case 'customer':
            htmlContent += `<td>${sale.customer}</td>`;
            break;
          case 'salesDate':
            htmlContent += `<td>${sale.salesDate}</td>`;
            break;
          case 'total':
            htmlContent += `<td>${sale.total}</td>`;
            break;
          case 'profit':
            htmlContent += `<td>${sale.profit}</td>`;
            break;
          case 'status':
            htmlContent += `<td>${sale.status}</td>`;
            break;
        }
      });
      
      htmlContent += `</tr>`;
    });
    
    htmlContent += `
        </tbody>
    </table>
    
    <div class="summary-section">
        <h3>Summary</h3>
        <p><strong>Total ${t('sales.title')}:</strong> ${data.length}</p>
        <p><strong>Export Date:</strong> ${new Date().toLocaleDateString()}</p>
    </div>
</body>
</html>`;

    const blob = new Blob([htmlContent], { type: 'application/vnd.ms-excel' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sales-report-${new Date().toISOString().split('T')[0]}.xls`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const handleExport = () => {
    const data = generateSalesExport();

    if (exportFormat === 'excel') {
      downloadSalesExcel(data);
    } else {
      downloadSalesHTML(data);
    }
    setShowExportModal(false);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm">
      {/* Header with New and Export Buttons */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">{t('sales.title')} Orders</h2>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowExportModal(true)}
              className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 cursor-pointer whitespace-nowrap"
            >
              <i className="ri-download-line w-4 h-4 flex items-center justify-center"></i>
              <span>{t('common.export')}</span>
              <i className="ri-arrow-down-s-line w-3 h-3 flex items-center justify-center"></i>
            </button>
            <Link href="/sales/new">
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium whitespace-nowrap cursor-pointer hover:bg-blue-700 transition-colors">
                {t('sales.newSale')}
              </button>
            </Link>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 whitespace-nowrap cursor-pointer"
            >
              <i className="ri-filter-line w-4 h-4 flex items-center justify-center"></i>
              <span>{t('common.filter')}</span>
              {hasActiveFilters && (
                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                  {Object.values(filters).filter(v => v !== '').length}
                </span>
              )}
            </button>
            {hasActiveFilters && (
              <button
                onClick={clearFilters}
                className="text-sm text-blue-600 hover:text-blue-800 cursor-pointer whitespace-nowrap"
              >
                Clear all
              </button>
            )}
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-500">
              {startItem}-{endItem} of {totalItems} {t('sales.title').toLowerCase()}
            </div>
            <div className="flex items-center space-x-2">
              <button 
                onClick={goToPrevPage}
                disabled={currentPage === 1}
                className="p-2 hover:bg-gray-100 rounded-lg cursor-pointer transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <i className="ri-arrow-left-s-line w-5 h-5 flex items-center justify-center text-gray-600"></i>
              </button>
              <span className="text-sm text-gray-600 px-2">
                Page {currentPage} of {totalPages}
              </span>
              <button 
                onClick={goToNextPage}
                disabled={currentPage === totalPages}
                className="p-2 hover:bg-gray-100 rounded-lg cursor-pointer transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <i className="ri-arrow-right-s-line w-5 h-5 flex items-center justify-center text-gray-600"></i>
              </button>
            </div>
            <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded transition-all duration-200 cursor-pointer ${
                  viewMode === 'list' 
                    ? 'bg-white text-gray-900 shadow-sm' 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                title="List View"
              >
                <i className="ri-list-unordered w-4 h-4 flex items-center justify-center"></i>
              </button>
              <button
                onClick={() => setViewMode('card')}
                className={`p-2 rounded transition-all duration-200 cursor-pointer ${
                  viewMode === 'card' 
                    ? 'bg-white text-gray-900 shadow-sm' 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                title="Card View"
              >
                <i className="ri-layout-grid-line w-4 h-4 flex items-center justify-center"></i>
              </button>
              <button
                onClick={() => setViewMode('kanban')}
                className={`p-2 rounded transition-all duration-200 cursor-pointer ${
                  viewMode === 'kanban' 
                    ? 'bg-white text-gray-900 shadow-sm' 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                title="Kanban View"
              >
                <i className="ri-layout-column-line w-4 h-4 flex items-center justify-center"></i>
              </button>
            </div>
          </div>
        </div>

        {/* Quick Status Filter Cards - Fixed to use translated display */}
        <div className="flex items-center space-x-3 flex-wrap gap-y-2 mt-4">
          <span className="text-sm font-medium text-gray-600 whitespace-nowrap">Quick filters:</span>
          {statusOptions.map(status => (
            <button
              key={status}
              onClick={() => setStatusFilter(status)}
              className={`inline-flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 cursor-pointer whitespace-nowrap ${
                filters.status === status
                  ? 'bg-blue-100 text-blue-700 border border-blue-300 shadow-sm'
                  : 'bg-gray-100 text-gray-600 border border-gray-200 hover:bg-gray-200'
              }`}
            >
              <span>{getTranslatedStatus(status)}</span>
              <span
                className={`inline-flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-bold rounded-full ${
                  filters.status === status
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-500 text-white'
                }`}
              >
                {getStatusCount(status)}
              </span>
            </button>
          ))}
          <div className="h-6 border-l border-gray-300 mx-3"></div>
          <span className="text-sm font-medium text-gray-600 whitespace-nowrap">{t('common.date')}:</span>
          <button
            onClick={setThisWeek}
            className="px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 whitespace-nowrap cursor-pointer"
          >
            This Week
          </button>
          <button
            onClick={setThisMonth}
            className="px-3 py-2 text-sm font-medium text-green-600 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 whitespace-nowrap cursor-pointer"
          >
            This Month
          </button>
        </div>
      </div>

      {/* Export Modal */}
      {showExportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">{t('common.export')} {t('sales.title')} Report</h3>
              <button
                onClick={() => setShowExportModal(false)}
                className="text-gray-400 hover:text-gray-600 cursor-pointer"
              >
                <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
              </button>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">Export Format</label>
                <div className="space-y-3">
                  <label className="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                    <input
                      type="radio"
                      value="html"
                      checked={exportFormat === 'html'}
                      onChange={(e) => setExportFormat(e.target.value as any)}
                      className="mt-1 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-gray-900 mb-1">HTML Report</div>
                      <div className="space-y-1">
                        <div className="text-sm text-green-600 flex items-center">
                          <i className="ri-check-line w-4 h-4 flex items-center justify-center mr-1"></i>
                          <span>Professional formatting with summary</span>
                        </div>
                        <div className="text-sm text-green-600 flex items-center">
                          <i className="ri-check-line w-4 h-4 flex items-center justify-center mr-1"></i>
                          <span>Can be converted to PDF</span>
                        </div>
                      </div>
                      <div className="mt-2 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded font-medium">
                        Recommended for Reports
                      </div>
                    </div>
                  </label>
                  
                  <label className="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                    <input
                      type="radio"
                      value="excel"
                      checked={exportFormat === 'excel'}
                      onChange={(e) => setExportFormat(e.target.value as any)}
                      className="mt-1 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-gray-900 mb-1">Excel Format</div>
                      <div className="space-y-1">
                        <div className="text-sm text-gray-500 flex items-center">
                          <i className="ri-bar-chart-line w-4 h-4 flex items-center justify-center mr-1"></i>
                          <span>Perfect for data analysis</span>
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <i className="ri-calculator-line w-4 h-4 flex items-center justify-center mr-1"></i>
                          <span>Supports calculations and formulas</span>
                        </div>
                      </div>
                    </div>
                  </label>
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-3">
                  <label className="block text-sm font-medium text-gray-700">Select Columns to Export</label>
                  <div className="text-xs text-gray-500">
                    {selectedExportColumns.length} of {getAvailableExportColumns().length} selected
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-2 max-h-60 overflow-y-auto border border-gray-200 rounded-lg p-3 bg-gray-50">
                  {getAvailableExportColumns().map(column => (
                    <label key={column.id} className="flex items-center space-x-2 py-2 px-3 bg-white rounded border border-gray-100 hover:border-gray-300 transition-colors cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedExportColumns.includes(column.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedExportColumns(prev => [...prev, column.id]);
                          } else {
                            setSelectedExportColumns(prev => prev.filter(id => id !== column.id));
                          }
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 w-4 h-4"
                      />
                      <span className="text-sm text-gray-700 truncate">
                        {column.name}
                      </span>
                    </label>
                  ))}
                </div>
                
                <div className="flex items-center space-x-2 mt-3">
                  <button
                    type="button"
                    onClick={() => setSelectedExportColumns(getAvailableExportColumns().map(col => col.id))}
                    className="text-xs text-blue-600 hover:text-blue-800 cursor-pointer font-medium"
                  >
                    Select All
                  </button>
                  <span className="text-gray-300">|</span>
                  <button
                    type="button"
                    onClick={() => setSelectedExportColumns([])}
                    className="text-xs text-red-600 hover:text-red-800 cursor-pointer font-medium"
                  >
                    Clear All
                  </button>
                  <span className="text-gray-300">|</span>
                  <button
                    type="button"
                    onClick={() => setSelectedExportColumns(['id', 'vendor', 'customer', 'total', 'status'])}
                    className="text-xs text-green-600 hover:text-green-800 cursor-pointer font-medium"
                  >
                    Essential Only
                  </button>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-sm text-blue-700">
                  <strong>Export Preview:</strong> This will export {filteredSales.length} sales records with the selected columns. 
                  {hasActiveFilters && ' Current filters are applied to the export.'}
                </p>
              </div>

              <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowExportModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap"
                >
                  {t('common.cancel')}
                </button>
                <button
                  type="button"
                  onClick={handleExport}
                  disabled={selectedExportColumns.length === 0}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 cursor-pointer flex items-center space-x-2 whitespace-nowrap"
                >
                  <i className="ri-download-line w-4 h-4 flex items-center justify-center"></i>
                  <span>{t('common.export')} {exportFormat === 'html' ? 'HTML' : 'Excel'}</span>
                  {selectedExportColumns.length > 0 && (
                    <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                      {selectedExportColumns.length}
                    </span>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Content Area - Better spacing for all views */}
      {viewMode === 'kanban' ? (
        <div className="p-6">
          <div className="grid grid-cols-4 gap-4">
            {statusOptions.map(status => {
              const statusSales = getSalesByStatus(status);
              const statusConfig = {
                'Completed': { 
                  color: 'bg-green-500', 
                  lightColor: 'bg-green-50',
                  borderColor: 'border-green-200',
                  textColor: 'text-green-700'
                },
                'Processing': { 
                  color: 'bg-blue-500', 
                  lightColor: 'bg-blue-50',
                  borderColor: 'border-blue-200',
                  textColor: 'text-blue-700'
                },
                'Shipped': { 
                  color: 'bg-purple-500', 
                  lightColor: 'bg-purple-50',
                  borderColor: 'border-purple-200',
                  textColor: 'text-purple-700'
                },
                'Delivered': { 
                  color: 'bg-gray-500', 
                  lightColor: 'bg-gray-50',
                  borderColor: 'border-gray-200',
                  textColor: 'text-gray-700'
                }
              };
              const config = statusConfig[status as keyof typeof statusConfig];
              
              return (
                <div
                  key={status}
                  className="bg-gray-50 rounded-lg"
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, status)}
                >
                  {/* Column Header */}
                  <div className="p-4 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className={`w-3 h-3 rounded-full ${config.color}`}></div>
                        <h3 className="text-sm font-semibold text-gray-900">{getTranslatedStatus(status)}</h3>
                      </div>
                      <span className="text-xs text-gray-500 bg-white px-2 py-1 rounded-full border">
                        {statusSales.length}
                      </span>
                    </div>
                  </div>
                  
                  {/* Cards Container */}
                  <div className="p-3 space-y-3 min-h-[500px] max-h-[600px] overflow-y-auto">
                    {statusSales.map(sale => (
                      <div
                        key={sale.id}
                        draggable
                        onDragStart={(e) => handleDragStart(e, sale.id)}
                        className={`bg-white rounded-xl border border-gray-200 p-4 cursor-move hover:shadow-xl transition-all duration-300 hover:scale-105 group ${
                          selectedSales.includes(sale.id) ? 'border-blue-400 shadow-lg ring-2 ring-blue-100' : 'hover:border-gray-300'
                        }`}
                        onClick={() => window.location.href = `/sales/${sale.id}`}
                      >
                        {/* Card Header */}
                        <div className="flex items-center justify-between mb-4">
                          <Link href={`/sales/${sale.id}`}>
                            <span className="text-base font-bold text-blue-600 hover:text-blue-800 transition-colors">
                              {sale.id}
                            </span>
                          </Link>
                          <div className="flex items-center space-x-3">
                            <button className="text-gray-300 hover:text-yellow-500 transition-all duration-200 hover:scale-110">
                              <i className="ri-star-line w-5 h-5 flex items-center justify-center"></i>
                            </button>
                            <input
                              type="checkbox"
                              className="rounded border-gray-300 w-5 h-5 text-blue-600 focus:ring-blue-500 cursor-pointer"
                              checked={selectedSales.includes(sale.id)}
                              onChange={() => toggleSaleSelection(sale.id)}
                            />
                          </div>
                        </div>
                        
                        {/* Vendor & Customer */}
                        <div className="space-y-3 mb-4">
                          <div className="bg-gray-50 rounded-lg p-3">
                            <div className="text-xs font-medium text-gray-500 mb-1 uppercase tracking-wide">{t('sales.vendor')}</div>
                            <div className="text-sm font-semibold text-gray-900 truncate" title={sale.vendor}>
                              {sale.vendor}
                            </div>
                          </div>
                          <div className="bg-gray-50 rounded-lg p-3">
                            <div className="text-xs font-medium text-gray-500 mb-1 uppercase tracking-wide">{t('sales.customer')}</div>
                            <div className="text-sm font-semibold text-gray-900 truncate" title={sale.customer}>
                              {sale.customer}
                            </div>
                          </div>
                        </div>
                        
                        {/* Sales Date */}
                        <div className="mb-4">
                          <div className="flex items-center space-x-2 bg-blue-50 rounded-lg p-3">
                            <i className="ri-calendar-2-line w-4 h-4 flex items-center justify-center text-blue-600"></i>
                            <div>
                              <div className="text-xs font-medium text-blue-600 mb-1 uppercase tracking-wide">{t('common.date')}</div>
                              <div className="text-sm font-semibold text-blue-800">{sale.salesDate}</div>
                            </div>
                          </div>
                        </div>
                        
                        {/* Financial Info */}
                        <div className="border-t border-gray-100 pt-4">
                          <div className="grid grid-cols-2 gap-3 mb-3">
                            <div className="text-center bg-green-50 rounded-lg p-3">
                              <div className="text-xs font-medium text-green-600 mb-1 uppercase tracking-wide">{t('common.total')}</div>
                              <div className="text-lg font-bold text-green-800">{sale.total}</div>
                            </div>
                            <div className="text-center bg-purple-50 rounded-lg p-3">
                              <div className="text-xs font-medium text-purple-600 mb-1 uppercase tracking-wide">Profit</div>
                              <div className="text-lg font-bold text-purple-800">{calculateProfit(sale.total, sale.commission)}</div>
                            </div>
                          </div>
                        </div>
                        
                        {/* Hover Effect Overlay */}
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-5/5 rounded-xl opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none"></div>
                      </div>
                    ))}
                    
                    {/* Empty State */}
                    {statusSales.length === 0 && (
                      <div className="flex flex-col items-center justify-center py-12 text-gray-400">
                        <div className="w-12 h-12 rounded-full border-2 border-dashed border-gray-300 flex items-center justify-center mb-3 bg-gray-50">
                          <i className="ri-drag-drop-line w-6 h-6 flex items-center justify-center"></i>
                        </div>
                        <div className="text-sm font-medium text-center text-gray-500">
                          Drop sales here
                        </div>
                        <div className="text-xs text-center text-gray-400 mt-1">
                          Drag cards from other columns
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ) : viewMode === 'list' ? (
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 w-4 h-4 cursor-pointer"
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedSales(currentSales.map(sale => sale.id));
                      } else {
                        setSelectedSales([]);
                      }
                    }}
                  />
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Reference
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  {t('sales.customer')}
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  {t('common.date')}
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  {t('common.total')} {t('common.amount')}
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  {t('common.status')}
                </th>
                <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  <i className="ri-more-2-line w-4 h-4 flex items-center justify-center mx-auto"></i>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-100">
              {currentSales.map((sale, index) => (
                <tr key={sale.id} className={`hover:bg-blue-25 transition-colors duration-150 ${selectedSales.includes(sale.id) ? 'bg-blue-50 border-l-4 border-blue-400' : ''}`}>
                  <td className="px-6 py-5 whitespace-nowrap">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300 w-4 h-4 text-blue-600 focus:ring-blue-500 cursor-pointer"
                      checked={selectedSales.includes(sale.id)}
                      onChange={() => toggleSaleSelection(sale.id)}
                    />
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap">
                    <div className="flex items-center">
                      <Link href={`/sales/${sale.id}`}>
                        <span className="text-sm font-semibold text-blue-600 hover:text-blue-800 cursor-pointer">
                          {sale.id}
                        </span>
                      </Link>
                    </div>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {sale.customer}
                    </div>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <i className="ri-calendar-line w-4 h-4 flex items-center justify-center text-gray-400"></i>
                      <span className="text-sm text-gray-700">
                        {new Date(sale.salesDate).toLocaleDateString()}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap">
                    <div className="text-sm font-semibold text-gray-900">{sale.total}</div>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap relative">
                    <button
                      onClick={() => setEditingStatus(editingStatus === sale.id ? null : sale.id)}
                      className={`inline-flex items-center px-3 py-2 text-xs font-semibold rounded-full whitespace-nowrap cursor-pointer hover:shadow-md transition-all duration-200 ${getStatusColor(sale.status)} border`}
                    >
                      <div className={`w-2 h-2 rounded-full mr-2 ${
                        sale.status === 'Processing' ? 'bg-yellow-500' :
                        sale.status === 'Shipped' ? 'bg-blue-500' :
                        sale.status === 'Delivered' ? 'bg-purple-500' :
                        'bg-green-500'
                      }`}></div>
                      {getTranslatedStatus(sale.status)}
                      <i className="ri-arrow-down-s-line ml-2 w-3 h-3 flex items-center justify-center"></i>
                    </button>
                    {editingStatus === sale.id && (
                      <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-xl z-10 min-w-[160px] py-2">
                        {statusOptions.map((status) => (
                          <button
                            key={status}
                            onClick={() => updateSaleStatus(sale.id, status)}
                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 whitespace-nowrap"
                          >
                            <div className="flex items-center">
                              <div className={`w-2 h-2 rounded-full mr-3 ${
                                status === 'Processing' ? 'bg-yellow-500' :
                                status === 'Shipped' ? 'bg-blue-500' :
                                status === 'Delivered' ? 'bg-purple-500' :
                                'bg-green-500'
                              }`}></div>
                              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(status)}`}>
                                {getTranslatedStatus(status)}
                              </span>
                            </div>
                          </button>
                        ))}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap text-center">
                    <button className="text-gray-400 hover:text-yellow-500 transition-colors duration-150 cursor-pointer">
                      <i className="ri-star-line w-4 h-4 flex items-center justify-center"></i>
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {currentSales.map((sale) => (
              <div
                key={sale.id}
                className={`relative bg-white border border-gray-200 rounded-xl p-5 hover:shadow-lg transition-all duration-200 cursor-pointer ${
                  selectedSales.includes(sale.id) ? 'border-blue-400 bg-blue-50' : ''
                }`}
                onClick={() => window.location.href = `/sales/${sale.id}`}
              >
                <div className="absolute top-4 right-4">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 w-4 h-4 text-blue-600 focus:ring-blue-500 cursor-pointer"
                    checked={selectedSales.includes(sale.id)}
                    onChange={() => toggleSaleSelection(sale.id)}
                  />
                </div>
                
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-3">
                    <Link href={`/sales/${sale.id}`}>
                      <span className="text-sm font-semibold text-blue-600 hover:text-blue-800">
                        {sale.id}
                      </span>
                    </Link>
                    <button className="text-gray-400 hover:text-yellow-500 transition-colors duration-150">
                      <i className="ri-star-line w-4 h-4 flex items-center justify-center"></i>
                    </button>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <div className="text-xs text-gray-500 mb-1">{t('sales.vendor')}</div>
                      <div className="text-sm font-medium text-gray-900">{sale.vendor}</div>
                    </div>
                    
                    <div>
                      <div className="text-xs text-gray-500 mb-1">{t('sales.customer')}</div>
                      <div className="text-sm text-gray-900">{sale.customer}</div>
                    </div>
                    
                    <div>
                      <div className="text-xs text-gray-500 mb-1">{t('common.date')}</div>
                      <div className="flex items-center space-x-2">
                        <i className="ri-calendar-line w-4 h-4 flex items-center justify-center text-gray-400"></i>
                        <span className="text-sm text-gray-700">{sale.salesDate}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="border-t border-gray-100 pt-4">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <div className="text-xs text-gray-500 mb-1">{t('common.total')} {t('common.amount')}</div>
                      <div className="text-sm font-semibold text-gray-900">{sale.total}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-xs text-gray-500 mb-1">Profit</div>
                      <div className="text-sm font-semibold text-green-600">
                        {calculateProfit(sale.total, sale.commission)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="relative">
                    <button
                      onClick={() => setEditingStatus(editingStatus === sale.id ? null : sale.id)}
                      className={`w-full inline-flex items-center justify-center px-3 py-2 text-xs font-semibold rounded-lg transition-all duration-200 ${getStatusColor(sale.status)} border hover:shadow-md`}
                    >
                      <div className={`w-2 h-2 rounded-full mr-2 ${
                        sale.status === 'Processing' ? 'bg-yellow-500' :
                        sale.status === 'Shipped' ? 'bg-blue-500' :
                        sale.status === 'Delivered' ? 'bg-purple-500' :
                        'bg-green-500'
                      }`}></div>
                      {getTranslatedStatus(sale.status)}
                      <i className="ri-arrow-down-s-line ml-2 w-3 h-3 flex items-center justify-center"></i>
                    </button>
                    {editingStatus === sale.id && (
                      <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-xl z-10 py-2">
                        {statusOptions.map((status) => (
                          <button
                            key={status}
                            onClick={() => updateSaleStatus(sale.id, status)}
                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                          >
                            <div className="flex items-center">
                              <div className={`w-2 h-2 rounded-full mr-3 ${
                                status === 'Processing' ? 'bg-yellow-500' :
                                status === 'Shipped' ? 'bg-blue-500' :
                                status === 'Delivered' ? 'bg-purple-500' :
                                'bg-green-500'
                              }`}></div>
                              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(status)}`}>
                                {getTranslatedStatus(status)}
                              </span>
                            </div>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="px-6 py-4 border-t border-gray-200 bg-white">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {startItem} to {endItem} of {totalItems} results
            </div>
            <div className="flex items-center space-x-2">
              <button 
                onClick={goToPrevPage}
                disabled={currentPage === 1}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer whitespace-nowrap"
              >
                {t('common.previous')}
              </button>
              
              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }
                  
                  return (
                    <button
                      key={pageNum}
                      onClick={() => goToPage(pageNum)}
                      className={`px-3 py-2 text-sm font-medium rounded-lg cursor-pointer whitespace-nowrap ${
                        currentPage === pageNum
                          ? 'bg-blue-600 text-white border border-blue-600'
                          : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>
              
              <button 
                onClick={goToNextPage}
                disabled={currentPage === totalPages}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer whitespace-nowrap"
              >
                {t('common.next')}
              </button>
            </div>
          </div>
        </div>
      )}
      
      {filteredSales.length === 0 && (
        <div className="text-center py-16">
          <i className="ri-file-list-3-line w-16 h-16 flex items-center justify-center mx-auto text-gray-300 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No {t('sales.title').toLowerCase()} found</h3>
          <p className="text-sm text-gray-500">Try adjusting your filters to see more results.</p>
        </div>
      )}
      
      {editingStatus && (
        <div className="fixed inset-0 z-5" onClick={() => setEditingStatus(null)} />
      )}
    </div>
  );
}
