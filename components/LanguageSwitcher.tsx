
'use client';

import { useState, useEffect } from 'react';
import I18n, { Language } from '@/lib/i18n';

export default function LanguageSwitcher() {
  const [currentLanguage, setCurrentLanguage] = useState<Language>('en');
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const i18n = I18n.getInstance();
    setCurrentLanguage(i18n.getCurrentLanguage());

    const unsubscribe = i18n.subscribe((language) => {
      setCurrentLanguage(language);
    });

    return unsubscribe;
  }, []);

  const handleLanguageChange = (language: Language) => {
    const i18n = I18n.getInstance();
    i18n.setLanguage(language);
    setIsOpen(false);
  };

  const getLanguageDisplay = (lang: Language) => {
    switch (lang) {
      case 'en':
        return { name: 'English', flag: '🇺🇸' };
      case 'zh':
        return { name: '中文', flag: '🇨🇳' };
      default:
        return { name: 'English', flag: '🇺🇸' };
    }
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg cursor-pointer whitespace-nowrap"
      >
        <span className="text-lg">{getLanguageDisplay(currentLanguage).flag}</span>
        <span className="hidden md:block">{getLanguageDisplay(currentLanguage).name}</span>
        <i className={`w-4 h-4 flex items-center justify-center transition-transform ${isOpen ? 'ri-arrow-up-s-line' : 'ri-arrow-down-s-line'}`}></i>
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
          <button
            onClick={() => handleLanguageChange('en')}
            className={`w-full flex items-center space-x-3 px-4 py-2 text-sm hover:bg-gray-50 cursor-pointer ${
              currentLanguage === 'en' ? 'text-blue-600 bg-blue-50' : 'text-gray-700'
            }`}
          >
            <span className="text-lg">🇺🇸</span>
            <span>English</span>
            {currentLanguage === 'en' && (
              <i className="ri-check-line w-4 h-4 flex items-center justify-center ml-auto"></i>
            )}
          </button>
          
          <button
            onClick={() => handleLanguageChange('zh')}
            className={`w-full flex items-center space-x-3 px-4 py-2 text-sm hover:bg-gray-50 cursor-pointer ${
              currentLanguage === 'zh' ? 'text-blue-600 bg-blue-50' : 'text-gray-700'
            }`}
          >
            <span className="text-lg">🇨🇳</span>
            <span>中文</span>
            {currentLanguage === 'zh' && (
              <i className="ri-check-line w-4 h-4 flex items-center justify-center ml-auto"></i>
            )}
          </button>
        </div>
      )}
    </div>
  );
}
