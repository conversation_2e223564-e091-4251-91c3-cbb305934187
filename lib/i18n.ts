
export interface Translations {
  [key: string]: string | Translations;
}

export const translations = {
  en: {
    common: {
      search: 'Search',
      filter: 'Filter',
      sort: 'Sort',
      actions: 'Actions',
      save: 'Save',
      cancel: 'Cancel',
      delete: 'Delete',
      edit: 'Edit',
      add: 'Add',
      view: 'View',
      export: 'Export',
      import: 'Import',
      loading: 'Loading...',
      noData: 'No data available',
      refresh: 'Refresh',
      back: 'Back',
      next: 'Next',
      previous: 'Previous',
      submit: 'Submit',
      close: 'Close',
      confirm: 'Confirm',
      status: 'Status',
      date: 'Date',
      amount: 'Amount',
      total: 'Total',
      name: 'Name',
      email: 'Email',
      phone: 'Phone',
      address: 'Address',
      notes: 'Notes',
      all: 'All',
      showing: 'Showing',
      of: 'of'
    },
    nav: {
      dashboard: 'Dashboard',
      sales: 'Sales',
      customers: 'Customers',
      products: 'Products',
      orders: 'Orders',
      payments: 'Payments',
      receiveMoneyTitle: 'Receive Money'
    },
    dashboard: {
      title: 'Dashboard',
      subtitle: 'Sales performance overview and key metrics',
      totalSales: 'Total Sales',
      totalOrders: 'Total Orders',
      totalCustomers: 'Total Customers',
      avgOrderValue: 'Avg Order Value',
      salesTrend: 'Sales Trend',
      recentSales: 'Recent Sales',
      topCustomers: 'Top Customers'
    },
    customers: {
      title: 'Customers',
      subtitle: 'Manage customer information and view their order history',
      addCustomer: 'Add New Customer',
      customerName: 'Customer Name',
      totalCustomers: 'Total Customers',
      active: 'Active',
      inactive: 'Inactive',
      noResults: 'No customers found',
      noResultsMessage: 'Try adjusting your search or filters to see more results.',
      customerInfo: 'Customer Information',
      tableHeaders: {
        name: 'Customer Name',
        totalOrders: 'Total Orders',
        totalAmount: 'Total Amount',
        commission: 'Commission',
        lastOrder: 'Last Order',
        status: 'Status',
        actions: 'Actions',
        viewDetails: 'View Details'
      },
      addCustomerModal: {
        title: 'Add New Customer',
        customerNameRequired: 'Customer name is required',
        customerExists: 'A customer with this name already exists',
        customerAdded: 'Customer has been added successfully!',
        company: 'Company',
        infoNote: 'This customer will be added to your system and can be selected when creating new sales orders. Order history will be automatically tracked once they make their first purchase.'
      }
    },
    customerDetail: {
      customerProfile: 'Customer profile and financial overview',
      totalOrders: 'Total Orders',
      totalPaid: 'Total Paid',
      outstanding: 'Outstanding',
      customerTimeline: 'Customer Timeline',
      firstOrder: 'First Order',
      lastOrder: 'Last Order',
      orderHistory: 'Order History',
      paymentTracking: 'Payment Tracking',
      customerStatement: 'Customer Statement',
      recordPayment: 'Record Payment',
      receiveMoney: 'Receive Money',
      exportStatement: 'Export Statement',
      statementPeriod: 'Statement Period: All Time',
      currentBalance: 'Current Balance',
      customerNotFound: 'Customer not found',
      noOrdersFound: 'No orders found for customer',
      backToCustomers: 'Back to Customers',
      allStatus: 'All Status',
      sortBy: 'Sort by:',
      type: 'Type',
      reference: 'Reference',
      amount: 'Amount',
      paid: 'Paid',
      dueDate: 'Due Date',
      method: 'Method',
      viewDetails: 'View Details',
      items: 'items',
      item: 'item',
      description: 'Description',
      referenceNumber: 'Reference Number',
      debitAmount: 'Debit Amount',
      creditAmount: 'Credit Amount',
      balance: 'Balance',
      invoice: 'Invoice',
      advance: 'Advance',
      paymentReceived: 'Payment Received',
      advancePayment: 'Advance Payment',
      tableHeaders: {
        orderId: 'Order ID',
        salesRep: 'Sales Rep',
        date: 'Date',
        products: 'Products',
        total: 'Total',
        status: 'Status',
        actions: 'Actions'
      },
      paymentModal: {
        title: 'Record Payment',
        paymentAmount: 'Payment Amount',
        paymentMethod: 'Payment Method',
        selectMethod: 'Select method',
        cash: 'Cash',
        creditCard: 'Credit Card',
        bankTransfer: 'Bank Transfer',
        check: 'Check',
        paypal: 'PayPal',
        paymentNotes: 'Payment notes...',
        notesOptional: 'Notes (Optional)',
        cancel: 'Cancel',
        recordPaymentBtn: 'Record Payment'
      },
      advancePaymentModal: {
        title: 'Receive Advance Payment',
        advancePaymentNotes: 'Advance payment notes...',
        receivePaymentBtn: 'Receive Payment'
      }
    },
    sales: {
      title: 'Sales',
      subtitle: 'Manage sales orders and track performance',
      newSale: 'New Sale',
      saleId: 'Sale ID',
      customer: 'Customer',
      salesRep: 'Sales Rep',
      vendor: 'Vendor',
      products: 'Products',
      items: 'items',
      item: 'item',
      total: 'Total',
      deliveryDate: 'Delivery Date',
      status: 'Status',
      statuses: {
        pending: 'Pending',
        confirmed: 'Confirmed',
        shipped: 'Shipped',
        delivered: 'Delivered',
        cancelled: 'Cancelled',
        completed: 'Completed',
        processing: 'Processing'
      },
      newSale: {
        title: 'Create New Sales Record',
        subtitle: 'Create a new sales invoice and manage product details',
        salesInformation: 'Sales Information',
        selectCustomer: 'Select customer',
        enterSalesRep: 'Enter sales representative',
        photo: 'Photo',
        productName: 'Product Name',
        unitPrice: 'Unit Price',
        quantity: 'Quantity',
        actions: 'Actions',
        productNamePlaceholder: 'Enter product name',
        totalAmount: 'Total Amount',
        addProduct: 'Add Product',
        addCustomColumn: 'Add Custom Column',
        createSalesRecord: 'Create Sales Record',
        creating: 'Creating...',
        successMessage: 'Sales record created successfully! Redirecting...',
        selectOption: 'Select option',
        enter: 'Enter',
        addCustomColumnModal: {
          title: 'Add Custom Column',
          columnName: 'Column Name',
          enterColumnName: 'Enter column name',
          dataType: 'Data Type',
          text: 'Text',
          number: 'Number',
          date: 'Date',
          dropdown: 'Dropdown',
          options: 'Options',
          option: 'Option',
          addOption: 'Add Option',
          addColumn: 'Add Column'
        }
      },
      detail: {
        title: 'Sales Order',
        viewAndManage: 'View and manage sales order details',
        exportOrder: 'Export Order',
        addColumn: 'Add Column',
        editOrder: 'Edit Order',
        saveChanges: 'Save Changes',
        orderInformation: 'Order Information',
        salesRepresentative: 'Sales Representative',
        deliveryDate: 'Delivery Date',
        notSpecified: 'Not specified',
        orderDate: 'Order Date',
        orderId: 'Order ID',
        addProduct: 'Add Product',
        photo: 'Photo',
        product: 'Product',
        sku: 'SKU',
        unitPrice: 'Unit Price',
        quantity: 'Quantity',
        actions: 'Actions',
        save: 'Save',
        edit: 'Edit',
        financialSummary: 'Financial Summary',
        totalAmount: 'Total Amount',
        netAmount: 'Net Amount',
        quickActions: 'Quick Actions',
        printInvoice: 'Print Invoice',
        downloadPdf: 'Download PDF',
        sendToWeChat: 'Send to WeChat',
        sendToWhatsApp: 'Send to WhatsApp',
        emailCustomer: 'Email Customer',
        orderTimeline: 'Order Timeline',
        orderCreated: 'Order Created',
        orderConfirmed: 'Order Confirmed',
        processingStarted: 'Processing Started',
        orderShipped: 'Order Shipped',
        orderDelivered: 'Order Delivered',
        today: 'Today',
        salesRecordNotFound: 'Sales record not found',
        salesRecordNotFoundMessage: 'The sales record you\'re looking for doesn\'t exist.',
        backToSales: 'Back to Sales',
        exportOptions: 'Export Options',
        customExport: 'Custom Export',
        customExportDesc: 'Choose format and columns',
        quickHtmlExport: 'Quick HTML Export',
        quickHtmlExportDesc: 'Professional report with all data',
        quickExcelExport: 'Quick Excel Export',
        quickExcelExportDesc: 'Spreadsheet with all data',
        addNewProduct: 'Add New Product',
        productPhoto: 'Product Photo',
        productNameRequired: 'Product Name *',
        enterProductName: 'Enter product name',
        enterSku: 'Enter SKU (optional)',
        unitPriceRequired: 'Unit Price *',
        quantityRequired: 'Quantity *',
        customFields: 'Custom Fields',
        totalAmountPreview: 'Total Amount:',
        required: 'Required field',
        addCustomColumn: 'Add Custom Column',
        columnName: 'Column Name',
        enterColumnName: 'Enter column name',
        dataType: 'Data Type',
        text: 'Text',
        number: 'Number',
        date: 'Date',
        dropdown: 'Dropdown',
        options: 'Options',
        addOption: 'Add Option',
        addColumn: 'Add Column',
        exportSalesOrder: 'Export Sales Order',
        exportFormat: 'Export Format',
        htmlReport: 'HTML Report',
        htmlReportDesc1: 'Professional formatting with complete order details',
        htmlReportDesc2: 'Includes financial summary and product breakdown',
        htmlReportDesc3: 'Can be converted to PDF or printed',
        recommendedForReports: 'Recommended for Reports',
        excelFormat: 'Excel Format',
        excelFormatDesc1: 'Perfect for data analysis and calculations',
        excelFormatDesc2: 'Structured tables with order and product data',
        excelFormatDesc3: 'Includes custom columns and financial metrics',
        selectDataSections: 'Select Data Sections to Export',
        selected: 'selected',
        of: 'of',
        selectAll: 'Select All',
        clearAll: 'Clear All',
        essentialOnly: 'Essential Only',
        exportPreview: 'Export Preview:',
        exportPreviewText: 'This will export the complete sales order',
        withSelectedData: 'with the selected data sections including products, financial summary, and custom fields.',
        exportHtml: 'Export HTML',
        exportExcel: 'Export Excel',
        sendInvoiceToWeChat: 'Send Invoice to WeChat',
        sendMethod: 'Send Method',
        templateMessage: 'Template Message',
        templateMessageDesc: 'Send as a formatted template message with invoice details',
        recommended: 'Recommended',
        directMessage: 'Direct Message',
        directMessageDesc: 'Send as a text message with custom content',
        wechatOpenId: 'WeChat OpenID *',
        enterWechatOpenId: 'Enter WeChat OpenID',
        wechatOpenIdDesc: 'The unique WeChat OpenID of the recipient',
        recipientName: 'Recipient Name',
        enterRecipientName: 'Enter recipient name (optional)',
        recipientNameDesc: 'Display name for the recipient',
        additionalMessage: 'Additional Message',
        additionalMessagePlaceholder: 'Add a custom message to include with the invoice (optional)',
        charactersLimit: 'characters',
        invoicePreview: 'Invoice Preview',
        sending: 'Sending...',
        sendToWeChatBtn: 'Send to WeChat',
        wechatIntegrationRequirements: 'WeChat Integration Requirements:',
        wechatReq1: 'Valid WeChat OpenID is required',
        wechatReq2: 'Template messages provide better formatting and delivery rates',
        wechatReq3: 'Make sure your WeChat app has proper messaging permissions',
        wechatReq4: 'Test with a known OpenID before sending to customers',
        sendInvoiceToWhatsApp: 'Send Invoice to WhatsApp',
        whatsappPhoneNumber: 'WhatsApp Phone Number *',
        whatsappPhoneNumberPlaceholder: '+1234567890',
        whatsappPhoneNumberDesc: 'Include country code (e.g., +1234567890)',
        sendToWhatsAppBtn: 'Send to WhatsApp',
        whatsappIntegrationRequirements: 'WhatsApp Integration Requirements:',
        whatsappReq1: 'Valid phone number with country code is required',
        whatsappReq2: 'Template messages provide better formatting and delivery rates',
        whatsappReq3: 'Make sure your WhatsApp Business API has proper permissions',
        whatsappReq4: 'Test with a known phone number before sending to customers',
        remove: 'Remove',
        selectOption: 'Select...',
        enterPlaceholder: 'Enter'
      }
    },
    payments: {
      title: 'Payment Tracking',
      subtitle: 'Monitor payment status and manage receivables',
      invoice: 'Invoice',
      orderId: 'Order ID',
      paid: 'Paid',
      partial: 'Partial',
      pending: 'Pending',
      overdue: 'Overdue',
      advance: 'Advance',
      advancePayment: 'Advance Payment',
      advancePayments: 'Advance Payments',
      dueDate: 'Due Date',
      reference: 'Reference',
      type: 'Type'
    },
    products: {
      title: 'Products',
      subtitle: 'Manage product catalog and inventory',
      addProduct: 'Add Product',
      productName: 'Product Name',
      sku: 'SKU',
      unitPrice: 'Unit Price',
      stock: 'Stock',
      category: 'Category'
    },
    orders: {
      title: 'Orders',
      subtitle: 'View and manage customer orders',
      orderDetails: 'Order Details',
      shippingInfo: 'Shipping Information',
      deliveryDate: 'Delivery Date'
    }
  },
  zh: {
    common: {
      search: '搜索',
      filter: '筛选',
      sort: '排序',
      actions: '操作',
      save: '保存',
      cancel: '取消',
      delete: '删除',
      edit: '编辑',
      add: '添加',
      view: '查看',
      export: '导出',
      import: '导入',
      loading: '加载中...',
      noData: '暂无数据',
      refresh: '刷新',
      back: '返回',
      next: '下一步',
      previous: '上一步',
      submit: '提交',
      close: '关闭',
      confirm: '确认',
      status: '状态',
      date: '日期',
      amount: '金额',
      total: '总计',
      name: '姓名',
      email: '邮箱',
      phone: '电话',
      address: '地址',
      notes: '备注',
      all: '全部',
      showing: '显示',
      of: '共'
    },
    nav: {
      dashboard: '控制台',
      sales: '销售',
      customers: '客户',
      products: '产品',
      orders: '订单',
      payments: '付款',
      receiveMoneyTitle: '收款'
    },
    dashboard: {
      title: '控制台',
      subtitle: '销售业绩概览和关键指标',
      totalSales: '总销售额',
      totalOrders: '总订单数',
      totalCustomers: '总客户数',
      avgOrderValue: '平均订单价值',
      salesTrend: '销售趋势',
      recentSales: '最近销售',
      topCustomers: '重要客户'
    },
    customers: {
      title: '客户',
      subtitle: '管理客户信息和查看订单历史',
      addCustomer: '添加新客户',
      customerName: '客户姓名',
      totalCustomers: '客户总数',
      active: '活跃',
      inactive: '非活跃',
      noResults: '未找到客户',
      noResultsMessage: '请调整搜索或筛选条件以查看更多结果。',
      customerInfo: '客户信息',
      tableHeaders: {
        name: '客户姓名',
        totalOrders: '总订单数',
        totalAmount: '总金额',
        commission: '佣金',
        lastOrder: '最后订单',
        status: '状态',
        actions: '操作',
        viewDetails: '查看详情'
      },
      addCustomerModal: {
        title: '添加新客户',
        customerNameRequired: '客户姓名为必填项',
        customerExists: '该姓名的客户已存在',
        customerAdded: '客户添加成功！',
        company: '公司',
        infoNote: '此客户将被添加到您的系统中，在创建新销售订单时可以选择。一旦他们进行首次购买，订单历史将自动跟踪。'
      }
    },
    customerDetail: {
      customerProfile: '客户档案和财务概览',
      totalOrders: '总订单数',
      totalPaid: '已付总额',
      outstanding: '未结余额',
      customerTimeline: '客户时间线',
      firstOrder: '首次订单',
      lastOrder: '最后订单',
      orderHistory: '订单历史',
      paymentTracking: '付款跟踪',
      customerStatement: '客户对账单',
      recordPayment: '记录付款',
      receiveMoney: '收款',
      exportStatement: '导出对账单',
      statementPeriod: '对账单期间：全部',
      currentBalance: '当前余额',
      customerNotFound: '未找到客户',
      noOrdersFound: '未找到该客户的订单',
      backToCustomers: '返回客户',
      allStatus: '全部状态',
      sortBy: '排序：',
      type: '类型',
      reference: '参考号',
      amount: '金额',
      paid: '已付',
      dueDate: '到期日期',
      method: '方式',
      viewDetails: '查看详情',
      items: '项',
      item: '项',
      description: '描述',
      referenceNumber: '参考号码',
      debitAmount: '借方金额',
      creditAmount: '贷方金额',
      balance: '余额',
      invoice: '发票',
      advance: '预付',
      paymentReceived: '收到付款',
      advancePayment: '预付款',
      tableHeaders: {
        orderId: '订单ID',
        salesRep: '销售代表',
        date: '日期',
        products: '产品',
        total: '总计',
        status: '状态',
        actions: '操作'
      },
      paymentModal: {
        title: '记录付款',
        paymentAmount: '付款金额',
        paymentMethod: '付款方式',
        selectMethod: '选择方式',
        cash: '现金',
        creditCard: '信用卡',
        bankTransfer: '银行转账',
        check: '支票',
        paypal: 'PayPal',
        paymentNotes: '付款备注...',
        notesOptional: '备注（可选）',
        cancel: '取消',
        recordPaymentBtn: '记录付款'
      },
      advancePaymentModal: {
        title: '收取预付款',
        advancePaymentNotes: '预付款备注...',
        receivePaymentBtn: '收取付款'
      }
    },
    sales: {
      title: '销售',
      subtitle: '管理销售订单和跟踪业绩',
      newSale: '新销售',
      saleId: '销售ID',
      customer: '客户',
      salesRep: '销售代表',
      vendor: '供应商',
      products: '产品',
      items: '项',
      item: '项',
      total: '总计',
      deliveryDate: '交货日期',
      status: '状态',
      statuses: {
        pending: '待处理',
        confirmed: '已确认',
        shipped: '已发货',
        delivered: '已交付',
        cancelled: '已取消',
        completed: '已完成',
        processing: '处理中'
      },
      newSale: {
        title: '创建新销售记录',
        subtitle: '创建新的销售发票并管理产品详情',
        salesInformation: '销售信息',
        selectCustomer: '选择客户',
        enterSalesRep: '输入销售代表',
        photo: '照片',
        productName: '产品名称',
        unitPrice: '单价',
        quantity: '数量',
        actions: '操作',
        productNamePlaceholder: '输入产品名称',
        totalAmount: '总金额',
        addProduct: '添加产品',
        addCustomColumn: '添加自定义列',
        createSalesRecord: '创建销售记录',
        creating: '创建中...',
        successMessage: '销售记录创建成功！正在跳转...',
        selectOption: '选择选项',
        enter: '输入',
        addCustomColumnModal: {
          title: '添加自定义列',
          columnName: '列名称',
          enterColumnName: '输入列名称',
          dataType: '数据类型',
          text: '文本',
          number: '数字',
          date: '日期',
          dropdown: '下拉选择',
          options: '选项',
          option: '选项',
          addOption: '添加选项',
          addColumn: '添加列'
        }
      },
      detail: {
        title: '销售订单',
        viewAndManage: '查看和管理销售订单详情',
        exportOrder: '导出订单',
        addColumn: '添加列',
        editOrder: '编辑订单',
        saveChanges: '保存更改',
        orderInformation: '订单信息',
        salesRepresentative: '销售代表',
        deliveryDate: '交货日期',
        notSpecified: '未指定',
        orderDate: '订单日期',
        orderId: '订单ID',
        addProduct: '添加产品',
        photo: '照片',
        product: '产品',
        sku: '产品编码',
        unitPrice: '单价',
        quantity: '数量',
        actions: '操作',
        save: '保存',
        edit: '编辑',
        financialSummary: '财务摘要',
        totalAmount: '总金额',
        netAmount: '净金额',
        quickActions: '快捷操作',
        printInvoice: '打印发票',
        downloadPdf: '下载PDF',
        sendToWeChat: '发送到微信',
        sendToWhatsApp: '发送到WhatsApp',
        emailCustomer: '邮件客户',
        orderTimeline: '订单时间线',
        orderCreated: '订单创建',
        orderConfirmed: '订单确认',
        processingStarted: '开始处理',
        orderShipped: '订单发货',
        orderDelivered: '订单交付',
        today: '今天',
        salesRecordNotFound: '未找到销售记录',
        salesRecordNotFoundMessage: '您要查找的销售记录不存在。',
        backToSales: '返回销售',
        exportOptions: '导出选项',
        customExport: '自定义导出',
        customExportDesc: '选择格式和列',
        quickHtmlExport: '快速HTML导出',
        quickHtmlExportDesc: '包含所有数据的专业报告',
        quickExcelExport: '快速Excel导出',
        quickExcelExportDesc: '包含所有数据的电子表格',
        addNewProduct: '添加新产品',
        productPhoto: '产品照片',
        productNameRequired: '产品名称 *',
        enterProductName: '输入产品名称',
        enterSku: '输入产品编码（可选）',
        unitPriceRequired: '单价 *',
        quantityRequired: '数量 *',
        customFields: '自定义字段',
        totalAmountPreview: '总金额：',
        required: '必填字段',
        addCustomColumn: '添加自定义列',
        columnName: '列名称',
        enterColumnName: '输入列名称',
        dataType: '数据类型',
        text: '文本',
        number: '数字',
        date: '日期',
        dropdown: '下拉选择',
        options: '选项',
        addOption: '添加选项',
        addColumn: '添加列',
        exportSalesOrder: '导出销售订单',
        exportFormat: '导出格式',
        htmlReport: 'HTML报告',
        htmlReportDesc1: '专业格式包含完整订单详情',
        htmlReportDesc2: '包括财务摘要和产品明细',
        htmlReportDesc3: '可转换为PDF或打印',
        recommendedForReports: '推荐用于报告',
        excelFormat: 'Excel格式',
        excelFormatDesc1: '完美适用于数据分析和计算',
        excelFormatDesc2: '结构化表格包含订单和产品数据',
        excelFormatDesc3: '包括自定义列和财务指标',
        selectDataSections: '选择要导出的数据部分',
        selected: '已选择',
        of: '共',
        selectAll: '全选',
        clearAll: '清除全部',
        essentialOnly: '仅必要项',
        exportPreview: '导出预览：',
        exportPreviewText: '这将导出完整的销售订单',
        withSelectedData: '包含选定的数据部分，包括产品、财务摘要和自定义字段。',
        exportHtml: '导出HTML',
        exportExcel: '导出Excel',
        sendInvoiceToWeChat: '发送发票到微信',
        sendMethod: '发送方式',
        templateMessage: '模板消息',
        templateMessageDesc: '以格式化模板消息发送发票详情',
        recommended: '推荐',
        directMessage: '直接消息',
        directMessageDesc: '以自定义内容的文本消息发送',
        wechatOpenId: '微信OpenID *',
        enterWechatOpenId: '输入微信OpenID',
        wechatOpenIdDesc: '收件人的唯一微信OpenID',
        recipientName: '收件人姓名',
        enterRecipientName: '输入收件人姓名（可选）',
        recipientNameDesc: '收件人的显示名称',
        additionalMessage: '附加消息',
        additionalMessagePlaceholder: '添加要包含在发票中的自定义消息（可选）',
        charactersLimit: '字符',
        invoicePreview: '发票预览',
        sending: '发送中...',
        sendToWeChatBtn: '发送到微信',
        wechatIntegrationRequirements: '微信集成要求：',
        wechatReq1: '需要有效的微信OpenID',
        wechatReq2: '模板消息提供更好的格式和送达率',
        wechatReq3: '确保您的微信应用具有适当的消息权限',
        wechatReq4: '在发送给客户之前使用已知的OpenID进行测试',
        sendInvoiceToWhatsApp: '发送发票到WhatsApp',
        whatsappPhoneNumber: 'WhatsApp电话号码 *',
        whatsappPhoneNumberPlaceholder: '+1234567890',
        whatsappPhoneNumberDesc: '包含国家代码（例如：+1234567890）',
        sendToWhatsAppBtn: '发送到WhatsApp',
        whatsappIntegrationRequirements: 'WhatsApp集成要求：',
        whatsappReq1: '需要带国家代码的有效电话号码',
        whatsappReq2: '模板消息提供更好的格式和送达率',
        whatsappReq3: '确保您的WhatsApp商业API具有适当的权限',
        whatsappReq4: '在发送给客户之前使用已知电话号码进行测试',
        remove: '移除',
        selectOption: '选择...',
        enterPlaceholder: '输入'
      }
    },
    payments: {
      title: '付款跟踪',
      subtitle: '监控付款状态和管理应收账款',
      invoice: '发票',
      orderId: '订单ID',
      paid: '已付',
      partial: '部分付款',
      pending: '待付',
      overdue: '逾期',
      advance: '预付',
      advancePayment: '预付款',
      advancePayments: '预付款项',
      dueDate: '到期日期',
      reference: '参考号',
      type: '类型'
    },
    products: {
      title: '产品',
      subtitle: '管理产品目录和库存',
      addProduct: '添加产品',
      productName: '产品名称',
      sku: '产品编码',
      unitPrice: '单价',
      stock: '库存',
      category: '类别'
    },
    orders: {
      title: '订单',
      subtitle: '查看和管理客户订单',
      orderDetails: '订单详情',
      shippingInfo: '发货信息',
      deliveryDate: '交货日期'
    }
  }
};

export type Language = 'en' | 'zh';

class I18n {
  private static instance: I18n;
  private currentLanguage: Language = 'en';
  private listeners: ((language: Language) => void)[] = [];

  private constructor() {
    this.loadLanguageFromStorage();
  }

  static getInstance(): I18n {
    if (!I18n.instance) {
      I18n.instance = new I18n();
    }
    return I18n.instance;
  }

  private loadLanguageFromStorage(): void {
    if (typeof window !== 'undefined') {
      const savedLanguage = localStorage.getItem('language') as Language;
      if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'zh')) {
        this.currentLanguage = savedLanguage;
      }
    }
  }

  private saveLanguageToStorage(): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('language', this.currentLanguage);
    }
  }

  getCurrentLanguage(): Language {
    return this.currentLanguage;
  }

  setLanguage(language: Language): void {
    this.currentLanguage = language;
    this.saveLanguageToStorage();
    this.notifyListeners();
  }

  t(key: string): string {
    const keys = key.split('.');
    let current: any = translations[this.currentLanguage];
    
    for (const k of keys) {
      if (current && typeof current === 'object' && k in current) {
        current = current[k];
      } else {
        // Fallback to English if key not found
        current = translations.en;
        for (const fallbackKey of keys) {
          if (current && typeof current === 'object' && fallbackKey in current) {
            current = current[fallbackKey];
          } else {
            return key; // Return key if not found in fallback
          }
        }
        break;
      }
    }
    
    return typeof current === 'string' ? current : key;
  }

  subscribe(listener: (language: Language) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.currentLanguage));
  }
}

export default I18n;
