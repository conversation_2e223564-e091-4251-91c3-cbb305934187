
'use client';

import { useState, useEffect } from 'react';
import I18n, { Language } from '@/lib/i18n';

export function useTranslation() {
  const [currentLanguage, setCurrentLanguage] = useState<Language>('en');

  useEffect(() => {
    const i18n = I18n.getInstance();
    setCurrentLanguage(i18n.getCurrentLanguage());

    const unsubscribe = i18n.subscribe((language) => {
      setCurrentLanguage(language);
    });

    return unsubscribe;
  }, []);

  const t = (key: string): string => {
    const i18n = I18n.getInstance();
    return i18n.t(key);
  };

  return {
    t,
    currentLanguage,
    setLanguage: (language: Language) => {
      const i18n = I18n.getInstance();
      i18n.setLanguage(language);
    }
  };
}
